# Figma to Replit Migration Project

## Project Overview
This project has been migrated from Figma to Replit environment. It's a full-stack JavaScript application with a React frontend and Express backend, implementing a dashboard interface with analytics, data visualization, and content management features.

## Project Architecture

### Frontend (React + TypeScript)
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter for client-side routing
- **UI Components**: Radix UI + shadcn/ui components
- **Styling**: Tailwind CSS with custom design system
- **State Management**: TanStack Query for server state
- **Build Tool**: Vite

### Backend (Express + TypeScript)
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Passport.js with local strategy
- **Session Management**: Express-session with PostgreSQL store

### Key Features
- Dashboard with multiple sections (Analytics, Overview, Statistics, etc.)
- Interactive data visualization with Chart.js (<PERSON><PERSON><PERSON>, Line, Bar charts)
- Word cloud visualization using react-d3-cloud
- Date picker with calendar interface
- Navigation and content management
- Responsive design
- Toggle between different visualization modes

## Recent Changes
- **Date**: August 06, 2025
- **Migration**: Figma design successfully migrated to Replit environment
- **Chart Integration**: Added interactive charts using react-chartjs-2, chart.js, react-d3-cloud, @types/d3-cloud
  - DataVisualizationSection: Interactive doughnut and line charts replacing static images
  - AnalyticsSection: Interactive sentiment analysis doughnut and bar charts
  - TagsSection: Word cloud visualization with toggle between badges and cloud view
- **Status**: Project running successfully on port 5000 with enhanced visualization features

## User Preferences
None specified yet.

## Security Considerations
- Client-server separation maintained
- Environment variables for sensitive data
- Session-based authentication
- Input validation with Zod schemas

## Development Notes
- Uses unified port 5000 for both frontend and backend in development
- Vite dev server integrated with Express backend
- Hot reload enabled for development
- All dependencies properly installed and configured