# Atomic Design Component Structure

This project follows the Atomic Design methodology to organize components into a scalable and maintainable structure.

## Structure Overview

```
components/
├── atoms/          # Basic building blocks
├── molecules/      # Combinations of atoms
├── organisms/      # Complex UI components
├── templates/      # Page layouts
└── ui/            # Existing shadcn/ui components
```

## Atoms (Basic Building Blocks)

### Text
Standardized text component with consistent typography variants.
```tsx
<Text variant="h1" color="primary">Title</Text>
<Text variant="body-sm" color="secondary">Subtitle</Text>
```

### Icon
Consistent icon component with size and color variants.
```tsx
<Icon icon={ArrowUpIcon} size="md" color="success" />
```

### Metric
Display metrics with optional trend indicators.
```tsx
<Metric 
  value="12,450" 
  label="Total Mentions"
  trend={{ value: "23%", direction: "up", description: "vs last period" }}
/>
```

### Tag
Standardized tag/badge component.
```tsx
<Tag variant="secondary" size="md">#hashtag</Tag>
```

### ChartContainer
Wrapper for chart components with consistent sizing.
```tsx
<ChartContainer height="300px">
  <BarChart data={data} />
</ChartContainer>
```

### Logo & Avatar
Consistent branding and user profile components.

## Molecules (Combinations of Atoms)

### StatCard
Combines Text, Icon, and Metric atoms for statistics display.
```tsx
<StatCard
  title="Total Mentions"
  subtitle="Last 30 days"
  value="12,450"
  trend={{ value: "23%", direction: "up" }}
  icon={LineChartIcon}
/>
```

### UserProfile
Combines Avatar and Text for user information.
```tsx
<UserProfile
  name="John Doe"
  avatar="/avatar.jpg"
  fallback="JD"
/>
```

### TagList
Manages collections of tags with optional toggle functionality.
```tsx
<TagList 
  tags={["#tag1", "#tag2", "#tag3"]}
  showToggle={true}
  toggleText="Show less"
/>
```

### ChartLegend
Standardized legend component for charts.
```tsx
<ChartLegend
  items={[
    { label: "Positive", percentage: "48%", color: "bg-green-500" },
    { label: "Negative", percentage: "23%", color: "bg-red-500" }
  ]}
/>
```

### ActionButton
Enhanced button with icon and text combinations.
```tsx
<ActionButton
  icon={DownloadIcon}
  variant="outline"
  onClick={handleDownload}
>
  Download
</ActionButton>
```

## Organisms (Complex UI Components)

### Navigation
Complete navigation header with logo, title, and user profile.
```tsx
<Navigation
  logoSrc="/logo.png"
  title="Dashboard"
  user={{ name: "John Doe", avatar: "/avatar.jpg", fallback: "JD" }}
/>
```

### Header
Page header with title and action buttons.
```tsx
<Header
  title="Airlines New 2025"
  showDownloadButton={true}
  onDownload={handleDownload}
/>
```

### StatisticsGrid
Complex statistics layout with multiple stat cards and charts.
```tsx
<StatisticsGrid
  mentionsData={{
    value: "12,450",
    trend: { value: "23%", direction: "up" },
    chartData: chartData,
    chartOptions: chartOptions
  }}
  sourceData={{ value: "123", subtitle: "Page/Group/Profile" }}
  engagementData={{
    value: "1,234",
    trend: { value: "23%", direction: "up" }
  }}
/>
```

## Benefits of This Structure

1. **Reusability**: Components can be easily reused across different parts of the application
2. **Consistency**: Standardized design tokens and patterns
3. **Maintainability**: Changes to base components automatically propagate
4. **Testability**: Smaller components are easier to test in isolation
5. **Scalability**: New features can be built by composing existing components

## Usage Examples

### Before (Original Section)
```tsx
export const NavigationSection = () => {
  return (
    <header className="w-full h-20 flex items-center justify-between px-8 md:px-[142px] py-4 bg-[#fdfdfd] shadow-soft-shadow-xs">
      <div className="flex-shrink-0">
        <img className="w-[189px] h-[52px] object-cover" alt="Logo" src="/logo.png" />
      </div>
      <h1 className="font-heading-h3 text-[#141416]">Dashboard</h1>
      <div className="flex items-center gap-2">
        <Avatar className="w-8 h-8 border border-solid">
          <AvatarImage src="/avatar.png" />
          <AvatarFallback>DS</AvatarFallback>
        </Avatar>
        <span className="font-bodytext-sm-semibold text-[#141416]">Darrell Steward</span>
      </div>
    </header>
  );
};
```

### After (Atomic Structure)
```tsx
export const NavigationSection = () => {
  return <Navigation />;
};
```

The atomic structure dramatically reduces code duplication and improves maintainability while preserving the same visual appearance and functionality.
