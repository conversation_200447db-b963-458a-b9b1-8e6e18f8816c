# Campaign Management Demo

## Cấu trúc mới đã được tạo:

### 🏠 **Trang chủ (Campaign List)**
- URL: `/`
- Hi<PERSON><PERSON> thị danh sách tất cả campaigns
- Design giống như bạn đã cung cấp
- <PERSON><PERSON> thể click "Preview" để vào chi tiết campaign

### 📊 **Trang chi tiết Campaign**
- URL: `/campaign/:id`
- Trang dashboard cũ đã được chuyển thành trang chi tiết
- C<PERSON> nút "Back" để quay về danh sách campaign

## Components đã tạo:

### Atoms:
- `StatusBadge`: Badge hiển thị trạng thái campaign (RUNNING, PAUSED, etc.)
- `KeywordTag`: Tag hiển thị keywords với style riêng

### Molecules:
- `CampaignCard`: Card hiển thị thông tin campaign với:
  - Tiêu đề campaign
  - Status badge
  - Danh sách keywords (tối đa 6, có "+X more" nếu nhiều hơn)
  - <PERSON><PERSON><PERSON> tạo
  - <PERSON>út Preview

### Organisms:
- `CampaignGrid`: Grid layout hiển thị danh sách campaigns
  - Header với title và số lượng campaigns
  - Grid responsive (1 cột mobile, 2 cột tablet, 3 cột desktop)

## Data mẫu:
```typescript
const campaigns = [
  {
    id: '1',
    title: 'Vay vốn mua nhà Q3 2...',
    status: 'RUNNING',
    keywords: ['vay vốn mua nhà', 'lãi suất vay mua nhà', 'hỗ trợ lãi suất', 'vay thế chấp số đỏ', '#vaymuanha'],
    moreKeywordsCount: 82,
    createdDate: '7/22/2025'
  },
  // ... more campaigns
];
```

## Navigation Flow:
1. User vào `/` → Thấy danh sách campaigns
2. Click "Preview" trên campaign → Chuyển đến `/campaign/:id`
3. Trong trang detail, click "Back" → Quay về `/`

## Styling:
- Sử dụng Tailwind CSS
- Màu sắc và spacing giống design gốc
- Responsive design
- Status badges có màu phù hợp:
  - RUNNING: Xanh lá
  - PAUSED: Vàng
  - COMPLETED: Xám
  - DRAFT: Xám nhạt

## Features:
- ✅ Campaign list với cards
- ✅ Status badges
- ✅ Keyword tags với "+X more"
- ✅ Responsive grid layout
- ✅ Navigation giữa list và detail
- ✅ Back button trong detail page
- ✅ Atomic design structure
