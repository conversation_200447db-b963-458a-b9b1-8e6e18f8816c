import React from 'react';
import { StatCard } from '@/components/molecules/StatCard';
import { ChartContainer } from '@/components/atoms/ChartContainer';
import { LineChartIcon, TargetIcon } from 'lucide-react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export interface StatisticsGridProps {
  mentionsData?: {
    value: string;
    trend: {
      value: string;
      direction: 'up' | 'down';
      description: string;
    };
    chartData: any;
    chartOptions: any;
  };
  sourceData?: {
    value: string;
    subtitle: string;
  };
  engagementData?: {
    value: string;
    trend: {
      value: string;
      direction: 'up' | 'down';
      description: string;
    };
  };
}

export const StatisticsGrid: React.FC<StatisticsGridProps> = ({
  mentionsData = {
    value: '12,450',
    trend: {
      value: '23%',
      direction: 'up',
      description: 'compared to previous period'
    },
    chartData: {},
    chartOptions: {}
  },
  sourceData = {
    value: '1,234',
    subtitle: 'Page/Group/Profile'
  },
  engagementData = {
    value: '1,234',
    trend: {
      value: '23%',
      direction: 'up',
      description: 'compared to previous period'
    }
  }
}) => {
  return (
    <section className="flex items-start gap-6 relative w-full">
      <StatCard
        title="Total Mentions"
        subtitle="Total mentions in last 30 days"
        value={mentionsData.value}
        trend={mentionsData.trend}
        icon={LineChartIcon}
        className="flex-1"
      >
        <ChartContainer height="336px">
          <Bar data={mentionsData.chartData} options={mentionsData.chartOptions} />
        </ChartContainer>
      </StatCard>

      <div className="flex flex-col w-[335px] gap-6 self-stretch">
        <StatCard
          title="Total Source"
          subtitle={sourceData.subtitle}
          value={sourceData.value}
          icon={TargetIcon}
          className="flex-1"
        />

        <StatCard
          title="Total Engagement"
          subtitle="Total engagement in last 30 days"
          value={engagementData.value}
          trend={engagementData.trend}
          icon={LineChartIcon}
          className="flex-1"
        />
      </div>
    </section>
  );
};
