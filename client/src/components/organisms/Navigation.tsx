import React from 'react';
import { Logo } from '@/components/atoms/Logo';
import { Text } from '@/components/atoms/Text';
import { UserProfile } from '@/components/molecules/UserProfile';

export interface NavigationProps {
  logoSrc?: string;
  title?: string;
  user?: {
    name: string;
    avatar?: string;
    fallback: string;
  };
}

export const Navigation: React.FC<NavigationProps> = ({
  logoSrc = '/figmaAssets/socialtracking-net-1.png',
  title = 'Dashboard',
  user = {
    name: '<PERSON>',
    avatar: '/figmaAssets/img.png',
    fallback: 'DS'
  }
}) => {
  return (
    <header className="w-full h-20 flex items-center justify-between px-8 md:px-[142px] py-4 bg-[#fdfdfd] shadow-soft-shadow-xs">
      <div className="flex-shrink-0">
        <Logo
          src={logoSrc}
          alt="Socialtracking.net logo"
        />
      </div>

      <Text variant="h3" color="primary">
        {title}
      </Text>

      <UserProfile
        name={user.name}
        avatar={user.avatar}
        fallback={user.fallback}
      />
    </header>
  );
};
