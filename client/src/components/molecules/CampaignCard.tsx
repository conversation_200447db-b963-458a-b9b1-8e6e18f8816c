import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/atoms/Text';
import { StatusBadge } from '@/components/atoms/StatusBadge';
import { KeywordTag } from '@/components/atoms/KeywordTag';
import { cn } from '@/lib/utils';

export interface CampaignCardProps {
  id: string;
  title: string;
  status: 'RUNNING' | 'PAUSED' | 'COMPLETED' | 'DRAFT';
  keywords: string[];
  moreKeywordsCount?: number;
  createdDate: string;
  onPreview?: (id: string) => void;
  className?: string;
}

export const CampaignCard: React.FC<CampaignCardProps> = ({
  id,
  title,
  status,
  keywords,
  moreKeywordsCount,
  createdDate,
  onPreview,
  className
}) => {
  const displayKeywords = keywords.slice(0, 6);
  const hasMoreKeywords = moreKeywordsCount && moreKeywordsCount > 0;

  return (
    <Card className={cn('bg-white border border-[#e5e7eb] rounded-lg shadow-sm', className)}>
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <Text variant="h3" className="text-[#111827] font-semibold text-lg leading-tight">
            {title}
          </Text>
          <StatusBadge status={status} />
        </div>

        <div className="mb-4">
          <Text variant="body-sm" color="secondary" className="mb-2">
            Keywords
          </Text>
          {keywords.length === 0 ? (
            <Text variant="body-sm" color="muted" className="italic">
              No keywords
            </Text>
          ) : (
            <div className="flex flex-wrap gap-2">
              {displayKeywords.map((keyword, index) => (
                <KeywordTag key={index}>
                  {keyword}
                </KeywordTag>
              ))}
              {hasMoreKeywords && (
                <KeywordTag variant="more">
                  +{moreKeywordsCount} more
                </KeywordTag>
              )}
            </div>
          )}
        </div>

        <div className="mb-6">
          <Text variant="body-sm" color="secondary">
            Created
          </Text>
          <Text variant="body-sm" color="primary">
            {createdDate}
          </Text>
        </div>

        <Button 
          className="w-full bg-[#6366f1] hover:bg-[#4f46e5] text-white rounded-lg"
          onClick={() => onPreview?.(id)}
        >
          Preview
        </Button>
      </CardContent>
    </Card>
  );
};
