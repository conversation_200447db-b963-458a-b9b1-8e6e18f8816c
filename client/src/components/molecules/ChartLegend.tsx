import React from 'react';
import { Text } from '@/components/atoms/Text';
import { cn } from '@/lib/utils';

export interface LegendItem {
  label: string;
  percentage: string;
  color: string;
  textColor?: string;
}

export interface ChartLegendProps {
  items: LegendItem[];
  direction?: 'horizontal' | 'vertical';
  className?: string;
}

export const ChartLegend: React.FC<ChartLegendProps> = ({
  items,
  direction = 'horizontal',
  className
}) => {
  return (
    <div className={cn(
      'flex items-start gap-2',
      direction === 'vertical' ? 'flex-col' : 'flex-row justify-center',
      className
    )}>
      {items.map((item, index) => (
        <div key={index} className="flex items-center gap-3 w-full">
          <div className="flex items-center gap-3 flex-1 self-stretch">
            <div
              className={cn('relative w-4 h-4 rounded', item.color)}
            />
            <Text variant="body-sm" color="secondary">
              {item.label}
            </Text>
          </div>
          <Text 
            variant="body-sm-semibold" 
            className={item.textColor || 'text-[#141416]'}
          >
            {item.percentage}
          </Text>
        </div>
      ))}
    </div>
  );
};
