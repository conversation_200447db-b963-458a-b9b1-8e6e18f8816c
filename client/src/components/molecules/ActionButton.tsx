import React from 'react';
import { Button } from '@/components/ui/button';
import { Icon } from '@/components/atoms/Icon';
import { Text } from '@/components/atoms/Text';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ActionButtonProps {
  children: React.ReactNode;
  icon?: LucideIcon;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  className?: string;
}

export const ActionButton: React.FC<ActionButtonProps> = ({
  children,
  icon,
  variant = 'outline',
  size = 'md',
  onClick,
  className
}) => {
  const sizeClasses = {
    sm: 'h-8 gap-1 px-2 py-1',
    md: 'h-10 gap-2 px-3 py-2',
    lg: 'h-12 gap-3 px-4 py-3'
  };

  return (
    <Button 
      variant={variant} 
      className={cn(
        'rounded-2xl flex items-center',
        sizeClasses[size],
        className
      )}
      onClick={onClick}
    >
      {icon && <Icon icon={icon} size="md" />}
      <Text variant="body-sm" color="primary">
        {children}
      </Text>
    </Button>
  );
};
