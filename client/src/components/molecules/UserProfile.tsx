import React from 'react';
import { Avatar } from '@/components/atoms/Avatar';
import { Text } from '@/components/atoms/Text';
import { cn } from '@/lib/utils';

export interface UserProfileProps {
  name: string;
  avatar?: string;
  fallback: string;
  subtitle?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const UserProfile: React.FC<UserProfileProps> = ({
  name,
  avatar,
  fallback,
  subtitle,
  size = 'md',
  className
}) => {
  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Avatar 
        src={avatar} 
        alt={`${name} profile picture`} 
        fallback={fallback}
        size={size}
      />
      <div className="flex flex-col items-start">
        <Text variant="body-sm-semibold" color="primary">
          {name}
        </Text>
        {subtitle && (
          <Text variant="body-sm" color="muted">
            {subtitle}
          </Text>
        )}
      </div>
    </div>
  );
};
