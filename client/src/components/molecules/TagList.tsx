import React from 'react';
import { Tag } from '@/components/atoms/Tag';
import { Button } from '@/components/ui/button';
import { ArrowUpIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface TagListProps {
  tags: string[];
  showToggle?: boolean;
  onToggle?: () => void;
  toggleText?: string;
  className?: string;
}

export const TagList: React.FC<TagListProps> = ({
  tags,
  showToggle = false,
  onToggle,
  toggleText = 'Show less',
  className
}) => {
  return (
    <div className={cn('flex flex-wrap items-start gap-3 w-full', className)}>
      {tags.map((tag, index) => (
        <Tag key={`tag-${index}`}>
          {tag}
        </Tag>
      ))}

      {showToggle && (
        <Button
          variant="ghost"
          size="sm"
          className="h-10 rounded-2xl flex items-center gap-2 px-3 py-2"
          onClick={onToggle}
        >
          <ArrowUpIcon className="w-5 h-5" />
          <span className="font-bodytext-sm-md text-[14px] font-medium tracking-[0.2px] leading-5 text-[#141416]">
            {toggleText}
          </span>
        </Button>
      )}
    </div>
  );
};
