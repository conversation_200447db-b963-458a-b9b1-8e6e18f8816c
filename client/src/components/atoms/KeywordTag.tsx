import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export interface KeywordTagProps {
  children: React.ReactNode;
  variant?: 'default' | 'more';
  className?: string;
}

export const KeywordTag: React.FC<KeywordTagProps> = ({ 
  children, 
  variant = 'default',
  className 
}) => {
  return (
    <Badge
      variant="secondary"
      className={cn(
        'px-2 py-1 text-xs font-normal rounded-md',
        variant === 'default' 
          ? 'bg-[#e0e7ff] text-[#3730a3] hover:bg-[#c7d2fe]'
          : 'bg-[#f3f4f6] text-[#6b7280] hover:bg-[#e5e7eb]',
        className
      )}
    >
      {children}
    </Badge>
  );
};
