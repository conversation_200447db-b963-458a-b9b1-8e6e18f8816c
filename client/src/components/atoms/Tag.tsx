import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export interface TagProps {
  children: React.ReactNode;
  variant?: 'default' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const sizeVariants = {
  sm: 'px-2 py-1 text-xs',
  md: 'px-3 py-2 font-bodytext-md-md text-[16px] font-medium tracking-[0.2px] leading-6',
  lg: 'px-4 py-3 text-base'
};

export const Tag: React.FC<TagProps> = ({ 
  children, 
  variant = 'secondary', 
  size = 'md', 
  className 
}) => {
  return (
    <Badge
      variant={variant}
      className={cn(
        'bg-[#e6e7e9] hover:bg-[#e6e7e9] text-[#141416] rounded-lg whitespace-nowrap',
        sizeVariants[size],
        className
      )}
    >
      {children}
    </Badge>
  );
};
