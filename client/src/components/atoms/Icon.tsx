import React from 'react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface IconProps {
  icon: LucideIcon;
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'muted' | 'success' | 'error';
  className?: string;
}

const sizeVariants = {
  sm: 'w-4 h-4',
  md: 'w-5 h-5',
  lg: 'w-6 h-6'
};

const colorVariants = {
  primary: 'text-[#141416]',
  secondary: 'text-[#4e5255]',
  muted: 'text-[#909498]',
  success: 'text-[#2bb684]',
  error: 'text-[#f84242]'
};

export const Icon: React.FC<IconProps> = ({ 
  icon: IconComponent, 
  size = 'md', 
  color = 'primary', 
  className 
}) => {
  return (
    <IconComponent 
      className={cn(
        sizeVariants[size],
        colorVariants[color],
        className
      )}
    />
  );
};
