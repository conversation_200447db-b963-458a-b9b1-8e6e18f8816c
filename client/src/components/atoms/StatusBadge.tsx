import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export interface StatusBadgeProps {
  status: 'RUNNING' | 'PAUSED' | 'COMPLETED' | 'DRAFT';
  className?: string;
}

const statusVariants = {
  RUNNING: 'bg-[#10b981] text-white hover:bg-[#059669]',
  PAUSED: 'bg-[#f59e0b] text-white hover:bg-[#d97706]',
  COMPLETED: 'bg-[#6b7280] text-white hover:bg-[#4b5563]',
  DRAFT: 'bg-[#e5e7eb] text-[#374151] hover:bg-[#d1d5db]'
};

export const StatusBadge: React.FC<StatusBadgeProps> = ({ 
  status, 
  className 
}) => {
  return (
    <Badge
      className={cn(
        'px-2 py-1 text-xs font-medium rounded-md',
        statusVariants[status],
        className
      )}
    >
      {status}
    </Badge>
  );
};
