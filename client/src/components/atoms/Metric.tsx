import React from 'react';
import { cn } from '@/lib/utils';

export interface MetricProps {
  value: string | number;
  label?: string;
  trend?: {
    value: string;
    direction: 'up' | 'down';
    description?: string;
  };
  className?: string;
}

export const Metric: React.FC<MetricProps> = ({ 
  value, 
  label, 
  trend, 
  className 
}) => {
  return (
    <div className={cn('flex flex-col gap-1', className)}>
      {label && (
        <span className="font-bodytext-sm-md text-[#4e5255]">
          {label}
        </span>
      )}
      <span className="font-heading-h2 text-[#141416]">
        {value}
      </span>
      {trend && (
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <span className={cn(
              'font-bodytext-sm-md',
              trend.direction === 'up' ? 'text-[#2bb684]' : 'text-[#f84242]'
            )}>
              {trend.value}
            </span>
          </div>
          {trend.description && (
            <span className="font-bodytext-sm-md text-[#4e5255]">
              {trend.description}
            </span>
          )}
        </div>
      )}
    </div>
  );
};
