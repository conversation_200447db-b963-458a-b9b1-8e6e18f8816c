import React from 'react';
import { cn } from '@/lib/utils';

export interface ChartContainerProps {
  children: React.ReactNode;
  height?: string | number;
  width?: string | number;
  className?: string;
}

export const ChartContainer: React.FC<ChartContainerProps> = ({ 
  children, 
  height = '300px', 
  width = '100%', 
  className 
}) => {
  const heightStyle = typeof height === 'number' ? `${height}px` : height;
  const widthStyle = typeof width === 'number' ? `${width}px` : width;

  return (
    <div 
      className={cn('relative', className)}
      style={{ height: heightStyle, width: widthStyle }}
    >
      {children}
    </div>
  );
};
