import React from 'react';
import { cn } from '@/lib/utils';

export interface LogoProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
}

export const Logo: React.FC<LogoProps> = ({ 
  src, 
  alt, 
  width = 189, 
  height = 52, 
  className 
}) => {
  return (
    <img
      className={cn('object-cover', className)}
      style={{ width: `${width}px`, height: `${height}px` }}
      alt={alt}
      src={src}
    />
  );
};
