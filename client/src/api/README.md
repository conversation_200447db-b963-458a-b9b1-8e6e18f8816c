# API Services Structure

## Overview
The API services are organized into v1 and v2 to support different API versions with specific functionalities:

- **V1**: Basic CRUD operations (getCampaigns, getCampaign)
- **V2**: Advanced features like reports and analytics (getCampaignReport)

## Directory Structure

```
api/
├── config.ts                 # API configuration and endpoints
├── index.ts                  # Main exports
├── v1/                       # V1 API services
│   ├── base.ts              # V1 base service (uses /api/v1)
│   ├── index.ts             # V1 exports
│   └── services/
│       └── campaignService.ts
├── v2/                       # V2 API services  
│   ├── base.ts              # V2 base service (uses /api/v2)
│   ├── index.ts             # V2 exports
│   └── services/
│       └── campaignService.ts
└── services/
    ├── index.ts             # Unified API interface
    └── avatarService.ts     # Other services
```

## Usage Examples

### Using V1 Services (Basic CRUD)

```typescript
import { CampaignServiceV1 } from '@/api/v1';

// Get all campaigns
const campaigns = await CampaignServiceV1.getCampaigns();

// Get single campaign
const campaign = await CampaignServiceV1.getCampaign('campaign-id');

// Create campaign
const newCampaign = await CampaignServiceV1.createCampaign({
  name: 'New Campaign',
  status: 'active'
});
```

### Using V2 Services (Reports & Analytics)

```typescript
import { CampaignServiceV2 } from '@/api/v2';

// Get campaign report
const report = await CampaignServiceV2.getCampaignReport({
  id: 'campaign-id',
  endpoint: 'performance',
  from_date: '2025-01-01',
  to_date: '2025-01-31'
});

// Get campaign analytics
const analytics = await CampaignServiceV2.getCampaignAnalytics('campaign-id', {
  metrics: ['impressions', 'clicks', 'conversions'],
  granularity: 'day'
});
```

### Using Unified API Interface

```typescript
import { CampaignAPI } from '@/api/services';

// V1 operations
const campaigns = await CampaignAPI.getCampaigns();
const campaign = await CampaignAPI.getCampaign('id');

// V2 operations  
const report = await CampaignAPI.getCampaignReport({
  id: 'campaign-id',
  endpoint: 'analytics'
});
```

## API Endpoints

### V1 Endpoints (Base URL: /api/v1)
- `GET /campaign/` - Get all campaigns
- `GET /campaign/{id}` - Get campaign by ID
- `POST /campaign/` - Create campaign
- `PUT /campaign/{id}` - Update campaign
- `PATCH /campaign/{id}` - Partial update campaign
- `DELETE /campaign/{id}` - Delete campaign

### V2 Endpoints (Base URL: /api/v2)
- `GET /campaign/{id}/{endpoint}` - Get campaign report
- `GET /campaign/{id}/analytics` - Get campaign analytics
- `GET /campaign/{id}/insights` - Get campaign insights
- `GET /campaign/{id}/performance` - Get campaign performance
- `POST /campaign/bulk-reports` - Get bulk campaign reports

## Configuration

The API configuration supports both versions:

```typescript
// config.ts
export const API_PREFIX = '/api/v1';
export const API_PREFIX_V2 = '/api/v2';
export const API_URL = `${API_BASE_URL}${API_PREFIX}`;
export const API_URL2 = `${API_BASE_URL}${API_PREFIX_V2}`;
```

## Migration Guide

### From Legacy Service

**Before:**
```typescript
import campaignService from '@/api/services/campaignService';

const campaigns = await campaignService.getCampaigns();
const report = await campaignService.getCampaignReport({...});
```

**After:**
```typescript
import { CampaignAPI } from '@/api/services';

const campaigns = await CampaignAPI.getCampaigns(); // Uses V1
const report = await CampaignAPI.getCampaignReport({...}); // Uses V2
```

### Direct Version Access

```typescript
// For V1 only
import { CampaignServiceV1 } from '@/api/v1';

// For V2 only  
import { CampaignServiceV2 } from '@/api/v2';
```

## Benefits

1. **Version Separation**: Clear separation between API versions
2. **Backward Compatibility**: Legacy imports still work
3. **Type Safety**: Full TypeScript support for both versions
4. **Unified Interface**: Single import for all campaign operations
5. **Scalability**: Easy to add new versions or services

## Error Handling

Both V1 and V2 services include:
- Request/response interceptors
- Authentication token handling
- Consistent error formatting
- Network error handling
- 401 redirect handling

## Types

```typescript
// V1 Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

// V2 Types
export interface CampaignReportParams {
  id: string;
  endpoint: string;
  from_date?: string;
  to_date?: string;
  params?: Record<string, any>;
}

export interface CampaignReportResponse {
  campaign_id: string;
  report_type: string;
  data: any;
  generated_at: string;
  date_range: {
    from: string;
    to: string;
  };
  metadata?: Record<string, any>;
}
```
