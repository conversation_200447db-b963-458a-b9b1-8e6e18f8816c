import { Campaign } from '../../types/campaign';
import BaseApiService from '../base';
import { END_POINTS } from '../config';

class CampaignService {
  // Get all campaigns
  async getCampaigns() {
    return BaseApiService.get<Campaign[]>(END_POINTS.campaign);
  }

  // Get campaign by ID
  async getCampaign(id: string) {
    return BaseApiService.get<Campaign>(`${END_POINTS.campaign}${id}`);
  }


  // custom endpoint to get campaigns report
  async getCampaignReport(data: {
    id: string,
    endpoint: string,
    from_date?: string,
    to_date?: string,
    params?: Record<string, any>
  }) {
    const { id, endpoint, from_date, to_date, params = {} } = data;

    // Add date parameters if provided
    const queryParams = { ...params };
    if (from_date) queryParams.from_date = from_date;
    if (to_date) queryParams.to_date = to_date;

    return BaseApiService.get<Campaign>(`${END_POINTS.campaign}${id}/${endpoint}`, {
      params: queryParams
    });
  }
}

export default new CampaignService();
