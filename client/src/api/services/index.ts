// Unified API Services
// This file provides a clean interface for using both v1 and v2 APIs

import { CampaignServiceV1 } from '../v1';
import { CampaignServiceV2, CampaignReportParams } from '../v2';
import { fetchAvatarUrl } from './avatarService';

// Campaign API - combines v1 and v2 functionality
export const CampaignAPI = {
  // V1 Methods - Basic CRUD operations
  getCampaigns: () => CampaignServiceV1.getCampaigns(),
  getCampaign: (id: string) => CampaignServiceV1.getCampaign(id),
  createCampaign: (data: any) => CampaignServiceV1.createCampaign(data),
  updateCampaign: (id: string, data: any) => CampaignServiceV1.updateCampaign(id, data),
  deleteCampaign: (id: string) => CampaignServiceV1.deleteCampaign(id),
  patchCampaign: (id: string, data: any) => CampaignServiceV1.patchCampaign(id, data),

  // V2 Methods - Advanced reporting and analytics
  getCampaignReport: (params: CampaignReportParams) => CampaignServiceV2.getCampaignReport(params),
  getCampaignAnalytics: (id: string, params?: any) => CampaignServiceV2.getCampaignAnalytics(id, params),
  getCampaignInsights: (id: string, params?: any) => CampaignServiceV2.getCampaignInsights(id, params),
  getCampaignPerformance: (id: string, params?: any) => CampaignServiceV2.getCampaignPerformance(id, params),
  getBulkCampaignReports: (ids: string[], params?: any) => CampaignServiceV2.getBulkCampaignReports(ids, params),
};

// Avatar API
export const AvatarAPI = {
  fetchAvatarUrl,
};

// Export individual services for direct access if needed
export { CampaignServiceV1, CampaignServiceV2 };

// Export types
export type { CampaignReportParams, CampaignReportResponse } from '../v2';
