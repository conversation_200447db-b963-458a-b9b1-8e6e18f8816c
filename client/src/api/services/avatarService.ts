import { AVATAR_API_BASE_URL } from '../config';

/**
 * Fetch avatar URL from Big360 API
 * @param authorId - The author ID to get avatar for
 * @param authorName - Author name for fallback avatar
 * @returns Promise that resolves to the avatar URL
 */
export const fetchAvatarUrl = async (authorId: string, authorName: string = 'Unknown'): Promise<string> => {
  const apiUrl = `${AVATAR_API_BASE_URL}/fb-img/${authorId}?type=profile`;
  const response = await fetch(apiUrl);
  const avatarUrl = await response.text();

  const cleanUrl = avatarUrl.trim().replace(/^"/, '').replace(/"$/, '');

  if (!cleanUrl || cleanUrl === 'null' || cleanUrl === '') {
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(authorName)}&background=random&size=40`;
  }

  return cleanUrl;
};
