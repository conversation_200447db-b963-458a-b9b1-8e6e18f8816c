import { Campaign } from '../../../types/campaign';
import BaseApiServiceV1 from '../base';
import { END_POINTS } from '../../config';

class CampaignServiceV1 {
  // Get all campaigns
  async getCampaigns() {
    return BaseApiServiceV1.get<Campaign[]>(END_POINTS.campaign);
  }

  // Get campaign by ID
  async getCampaign(id: string) {
    return BaseApiServiceV1.get<Campaign>(`${END_POINTS.campaign}${id}`);
  }

  // Create new campaign
  async createCampaign(campaignData: Partial<Campaign>) {
    return BaseApiServiceV1.post<Campaign>(END_POINTS.campaign, campaignData);
  }

  // Update campaign
  async updateCampaign(id: string, campaignData: Partial<Campaign>) {
    return BaseApiServiceV1.put<Campaign>(`${END_POINTS.campaign}${id}`, campaignData);
  }

  // Delete campaign
  async deleteCampaign(id: string) {
    return BaseApiServiceV1.delete(`${END_POINTS.campaign}${id}`);
  }

  // Patch campaign (partial update)
  async patchCampaign(id: string, campaignData: Partial<Campaign>) {
    return BaseApiServiceV1.patch<Campaign>(`${END_POINTS.campaign}${id}`, campaignData);
  }
}

export default new CampaignServiceV1();
