import { Campaign } from '../../../types/campaign';
import BaseApiServiceV2 from '../base';
import { END_POINTS } from '../../config';

export interface CampaignReportParams {
  id: string;
  endpoint: string;
  from_date?: string;
  to_date?: string;
  params?: Record<string, any>;
}

export interface CampaignReportResponse {
  campaign_id: string;
  report_type: string;
  data: any;
  generated_at: string;
  date_range: {
    from: string;
    to: string;
  };
  metadata?: Record<string, any>;
}

class CampaignServiceV2 {
  // Get campaign report - enhanced version with better data structure
  async getCampaignReport(data: CampaignReportParams) {
    const { id, endpoint, from_date, to_date, params = {} } = data;

    // Add date parameters if provided
    const queryParams = { ...params };
    if (from_date) queryParams.from_date = from_date;
    if (to_date) queryParams.to_date = to_date;

    return BaseApiServiceV2.get<CampaignReportResponse>(
      `${END_POINTS.campaign}${id}/${endpoint}`, 
      {
        params: queryParams
      }
    );
  }

  // Get campaign analytics - new v2 endpoint
  async getCampaignAnalytics(id: string, params?: {
    metrics?: string[];
    granularity?: 'hour' | 'day' | 'week' | 'month';
    from_date?: string;
    to_date?: string;
  }) {
    return BaseApiServiceV2.get<any>(
      `${END_POINTS.campaign}${id}/analytics`,
      { params }
    );
  }

  // Get campaign insights - new v2 endpoint
  async getCampaignInsights(id: string, params?: {
    insight_type?: 'performance' | 'audience' | 'content' | 'trends';
    from_date?: string;
    to_date?: string;
  }) {
    return BaseApiServiceV2.get<any>(
      `${END_POINTS.campaign}${id}/insights`,
      { params }
    );
  }

  // Get campaign performance metrics - new v2 endpoint
  async getCampaignPerformance(id: string, params?: {
    metrics?: string[];
    compare_period?: boolean;
    from_date?: string;
    to_date?: string;
  }) {
    return BaseApiServiceV2.get<any>(
      `${END_POINTS.campaign}${id}/performance`,
      { params }
    );
  }

  // Bulk campaign reports - new v2 endpoint
  async getBulkCampaignReports(campaignIds: string[], params?: {
    report_types?: string[];
    from_date?: string;
    to_date?: string;
  }) {
    return BaseApiServiceV2.post<any>(
      `${END_POINTS.campaign}bulk-reports`,
      {
        campaign_ids: campaignIds,
        ...params
      }
    );
  }
}

export default new CampaignServiceV2();
