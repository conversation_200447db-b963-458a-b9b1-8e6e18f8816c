// API Usage Examples
// This file demonstrates how to use the new v1/v2 API structure

import { CampaignAPI, CampaignServiceV1, CampaignServiceV2 } from './services';
import type { CampaignReportParams } from './v2';

// Example 1: Using Unified CampaignAPI (Recommended)
export const unifiedAPIExamples = {
  // V1 operations (basic CRUD)
  async getAllCampaigns() {
    try {
      const response = await CampaignAPI.getCampaigns();
      console.log('All campaigns:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      throw error;
    }
  },

  async getSingleCampaign(id: string) {
    try {
      const response = await CampaignAPI.getCampaign(id);
      console.log('Campaign details:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching campaign:', error);
      throw error;
    }
  },

  // V2 operations (reports and analytics)
  async getCampaignReport(campaignId: string, dateRange?: { from: string; to: string }) {
    try {
      const params: CampaignReportParams = {
        id: campaignId,
        endpoint: 'performance',
        from_date: dateRange?.from || '2025-01-01',
        to_date: dateRange?.to || '2025-01-31'
      };

      const response = await CampaignAPI.getCampaignReport(params);
      console.log('Campaign report:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching campaign report:', error);
      throw error;
    }
  },

  async getCampaignAnalytics(campaignId: string) {
    try {
      const response = await CampaignAPI.getCampaignAnalytics(campaignId, {
        metrics: ['impressions', 'clicks', 'conversions'],
        granularity: 'day',
        from_date: '2025-01-01',
        to_date: '2025-01-31'
      });
      console.log('Campaign analytics:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching campaign analytics:', error);
      throw error;
    }
  }
};

// Example 2: Using V1 Service Directly
export const v1APIExamples = {
  async manageCampaigns() {
    try {
      // Get all campaigns
      const campaigns = await CampaignServiceV1.getCampaigns();
      
      // Create new campaign
      const newCampaign = await CampaignServiceV1.createCampaign({
        name: 'New Campaign 2025',
        status: 'active',
        keywords: ['keyword1', 'keyword2']
      });

      // Update campaign
      const updatedCampaign = await CampaignServiceV1.updateCampaign(newCampaign.data.id, {
        name: 'Updated Campaign Name'
      });

      // Partial update
      await CampaignServiceV1.patchCampaign(newCampaign.data.id, {
        status: 'paused'
      });

      return { campaigns, newCampaign, updatedCampaign };
    } catch (error) {
      console.error('Error in V1 operations:', error);
      throw error;
    }
  }
};

// Example 3: Using V2 Service Directly
export const v2APIExamples = {
  async getAdvancedReports(campaignId: string) {
    try {
      // Get detailed campaign report
      const report = await CampaignServiceV2.getCampaignReport({
        id: campaignId,
        endpoint: 'detailed-analytics',
        from_date: '2025-01-01',
        to_date: '2025-01-31',
        params: {
          include_demographics: true,
          include_sentiment: true
        }
      });

      // Get campaign insights
      const insights = await CampaignServiceV2.getCampaignInsights(campaignId, {
        insight_type: 'performance',
        from_date: '2025-01-01',
        to_date: '2025-01-31'
      });

      // Get performance metrics
      const performance = await CampaignServiceV2.getCampaignPerformance(campaignId, {
        metrics: ['reach', 'engagement', 'conversion_rate'],
        compare_period: true
      });

      // Get bulk reports for multiple campaigns
      const bulkReports = await CampaignServiceV2.getBulkCampaignReports(
        [campaignId, 'other-campaign-id'],
        {
          report_types: ['performance', 'demographics'],
          from_date: '2025-01-01',
          to_date: '2025-01-31'
        }
      );

      return { report, insights, performance, bulkReports };
    } catch (error) {
      console.error('Error in V2 operations:', error);
      throw error;
    }
  }
};

// Example 4: Real-world usage in React component
export const reactComponentExample = `
import React, { useState, useEffect } from 'react';
import { CampaignAPI } from '@/api/services';

const CampaignDashboard = ({ campaignId }: { campaignId: string }) => {
  const [campaign, setCampaign] = useState(null);
  const [report, setReport] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch basic campaign data (V1)
        const campaignResponse = await CampaignAPI.getCampaign(campaignId);
        setCampaign(campaignResponse.data);
        
        // Fetch campaign report (V2)
        const reportResponse = await CampaignAPI.getCampaignReport({
          id: campaignId,
          endpoint: 'performance',
          from_date: '2025-01-01',
          to_date: '2025-01-31'
        });
        setReport(reportResponse.data);
        
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [campaignId]);

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      <h1>{campaign?.name}</h1>
      <div>Status: {campaign?.status}</div>
      <div>Performance: {report?.performance_summary}</div>
    </div>
  );
};
`;

// Example 5: Error handling patterns
export const errorHandlingExample = {
  async robustAPICall(campaignId: string) {
    try {
      // Try V2 first for enhanced data
      const report = await CampaignAPI.getCampaignReport({
        id: campaignId,
        endpoint: 'enhanced-analytics'
      });
      return report.data;
    } catch (error) {
      console.warn('V2 API failed, falling back to V1:', error);
      
      try {
        // Fallback to V1
        const campaign = await CampaignAPI.getCampaign(campaignId);
        return campaign.data;
      } catch (fallbackError) {
        console.error('Both V1 and V2 failed:', fallbackError);
        throw new Error('Unable to fetch campaign data');
      }
    }
  }
};
