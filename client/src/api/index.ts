// Export configuration
export * from './config';

// Export base service
export { default as BaseApiService } from './base';

// Export axios instance
export { default as apiClient } from './axios';

// V1 API Services (for basic CRUD operations)
export { CampaignServiceV1 } from './v1';
export { fetchAvatarUrl } from './services/avatarService';

// V2 API Services (for advanced features like reports)
export { CampaignServiceV2 } from './v2';

// Legacy export for backward compatibility
export { CampaignServiceV1 as campaignService } from './v1';

// Export types
export type { ApiResponse } from './base';
export type {
  Tag,
  Campaign,
  TopicCloudResponse,
  TopicCloudToken,
  DemographicsData,
  DemographicsGender,
  HashtagAnalysisResponse,
  HashtagAnalysisItem,
  SentimentAnalysisResponse,
  SentimentDistribution,
  SentimentDistributionItem,
  SentimentTrend,
  SentimentHeadline,
  PerformanceOverviewResponse,
  PerformanceSummary,
  PerformanceSeries,
  PerformanceMetric,
  IntegrationsSourcesResponse,
  IntegrationsSourceItem,
  MentionPost,
  MentionPostEngagement,
  MentionPostsResponse,
  MentionAuthor,
  MentionAuthorsResponse,
  Comment,
  CommentAuthor,
  TopCommentsResponse
} from '../types/campaign';
