import React from 'react';
import { useLocation } from 'wouter';
import { Navigation } from '@/components/organisms/Navigation';
import { CampaignGrid } from '@/components/organisms/CampaignGrid';
import type { Campaign } from '@/components/organisms/CampaignGrid';

export const CampaignList = (): JSX.Element => {
  const [, setLocation] = useLocation();

  // Mock data based on the design you provided
  const campaigns: Campaign[] = [
    {
      id: '1',
      title: 'Vay vốn mua nhà Q3 2...',
      status: 'RUNNING',
      keywords: ['vay vốn mua nhà', 'lãi suất vay mua nhà', 'hỗ trợ lãi suất', 'vay thế chấp số đỏ', '#vaymuanha'],
      moreKeywordsCount: 82,
      createdDate: '7/22/2025'
    },
    {
      id: '2',
      title: 'Vay vốn',
      status: 'RUNNING',
      keywords: ['hỗ trợ vay', 'vay ngân hàng', 'vay tiền', 'vay tín chấp', 'khoản vay'],
      moreKeywordsCount: 84,
      createdDate: '8/3/2025'
    },
    {
      id: '3',
      title: 'Vay vốn 2',
      status: 'RUNNING',
      keywords: ['hỗ trợ vay', 'vay ngân hàng', 'vay tiền', 'vay tín chấp', 'khoản vay'],
      moreKeywordsCount: 84,
      createdDate: '8/5/2025'
    },
    {
      id: '4',
      title: 'Vay vốn 3',
      status: 'RUNNING',
      keywords: ['hỗ trợ vay', 'vay ngân hàng', 'vay tiền', 'vay tín chấp', 'khoản vay'],
      moreKeywordsCount: 84,
      createdDate: '8/7/2025'
    },
    {
      id: '5',
      title: 'Test Campaign 175534...',
      status: 'RUNNING',
      keywords: [],
      createdDate: '7/25/2025'
    },
    {
      id: '6',
      title: 'Test',
      status: 'RUNNING',
      keywords: [],
      createdDate: '7/25/2025'
    }
  ];

  const handlePreview = (campaignId: string) => {
    setLocation(`/campaign/${campaignId}`);
  };

  return (
    <div className="flex flex-col w-full items-start relative bg-[#f9fafb] min-h-screen">
      <Navigation 
        title="Campaign Management"
        user={{
          name: 'Darrell Steward',
          avatar: '/figmaAssets/img.png',
          fallback: 'DS'
        }}
      />
      
      <div className="flex flex-col w-full items-start gap-6 px-8 md:px-[142px] py-8 relative">
        <CampaignGrid
          campaigns={campaigns}
          totalCount={6}
          onPreview={handlePreview}
        />
      </div>
    </div>
  );
};
