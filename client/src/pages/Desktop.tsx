import { ArrowLeftIcon, CalendarIcon } from "lucide-react";
import React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { AnalyticsSection } from "./sections/AnalyticsSection";
import { ContentSection } from "./sections/ContentSection";
import { DataVisualizationSection } from "./sections/DataVisualizationSection";
import { HeaderSection } from "./sections/HeaderSection";
import { InsightsSection } from "./sections/InsightsSection";
import { NavigationSection } from "./sections/NavigationSection";
import { OverviewSection } from "./sections/OverviewSection";
import { StatisticsSection } from "./sections/StatisticsSection";
import { TagsSection } from "./sections/TagsSection";

export const Desktop = (): JSX.Element => {
  // CalendarIcon data for the date picker
  const weekDays = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
  const calendarDays = [
    // Previous month days (grayed out)
    { day: "27", currentMonth: false },
    { day: "28", currentMonth: false },
    { day: "29", currentMonth: false },
    { day: "30", currentMonth: false },
    { day: "31", currentMonth: false },
    // Current month days
    { day: "1", currentMonth: true },
    { day: "2", currentMonth: true },
    { day: "3", currentMonth: true },
    { day: "4", currentMonth: true, selected: true, rangeStart: true },
    { day: "5", currentMonth: true, inRange: true },
    { day: "6", currentMonth: true, inRange: true },
    { day: "7", currentMonth: true, inRange: true },
    { day: "8", currentMonth: true, inRange: true },
    { day: "9", currentMonth: true, inRange: true },
    { day: "10", currentMonth: true, inRange: true },
    { day: "11", currentMonth: true, inRange: true },
    { day: "12", currentMonth: true, inRange: true },
    { day: "13", currentMonth: true, selected: true, rangeEnd: true },
    { day: "14", currentMonth: true },
    { day: "15", currentMonth: true },
    { day: "16", currentMonth: true },
    { day: "17", currentMonth: true },
    { day: "18", currentMonth: true },
    { day: "19", currentMonth: true },
    { day: "20", currentMonth: true },
    { day: "21", currentMonth: true },
    { day: "22", currentMonth: true },
    { day: "23", currentMonth: true },
    { day: "24", currentMonth: true },
    { day: "25", currentMonth: true },
    { day: "26", currentMonth: true },
    { day: "27", currentMonth: true },
    { day: "28", currentMonth: true },
    { day: "29", currentMonth: true },
    { day: "30", currentMonth: true },
    { day: "31", currentMonth: true },
    // Next month days (grayed out)
    { day: "1", currentMonth: false },
    { day: "2", currentMonth: false },
    { day: "3", currentMonth: false },
    { day: "4", currentMonth: false },
    { day: "5", currentMonth: false },
    { day: "6", currentMonth: false },
  ];

  return (
    <div className="flex flex-col w-full items-start relative bg-[#fdfdfd]">
      <NavigationSection />
      <div className="flex flex-col w-full items-start gap-6 px-[142px] py-6 relative">
        <Button variant="outline" className="h-10 gap-2 px-3 py-2 rounded-2xl">
          <ArrowLeftIcon className="w-5 h-5" />
          <span className="font-bodytext-sm-md text-[#141416]">Back</span>
        </Button>

        <HeaderSection />

        <div className="font-bodytext-sm-semibold text-[#141416]">
          Total Keyword: 1,234
        </div>

        <TagsSection />

        <div className="flex w-full items-start justify-between relative">
          <h3 className="font-heading-h3 text-[#141416]">Overview</h3>

          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="h-10 gap-2 px-2 py-1 rounded-2xl border-[#c5c8cb] bg-[#fdfdfd]"
              >
                <CalendarIcon className="w-4 h-4" />
                <span className="font-bodytext-sm-md text-[#141416]">
                  Last 30 days
                </span>
                <CalendarIcon className="w-4 h-4 ml-auto" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Card className="border-[#d9dbde] shadow-soft-shadow-sm p-3 w-[370px]">
                <CardContent className="p-0 space-y-4">
                  <div className="flex items-center justify-between">
                    <Button
                      variant="outline"
                      size="icon"
                      className="rounded-xl border-[#c5c8cb]"
                    >
                      <img
                        className="w-5 h-5"
                        alt="Arrow left long line"
                        src="/figmaAssets/arrow-left-long-line.svg"
                      />
                    </Button>
                    <div className="flex items-center gap-1 font-bodytext-sm-md text-[#141416]">
                      <span>June,</span>
                      <span>2025</span>
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      className="rounded-xl border-[#c5c8cb]"
                    >
                      <img
                        className="w-5 h-5"
                        alt="Arrow right long"
                        src="/figmaAssets/arrow-right-long-line.svg"
                      />
                    </Button>
                  </div>

                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <div className="flex h-9 items-center p-2 bg-[#fdfdfd] rounded-xl border border-[#c5c8cb]">
                        <span className="font-bodytext-sm-md text-[#909498]">
                          11/05/2025
                        </span>
                      </div>
                    </div>
                    <span className="font-bodytext-sm-md text-[#909498]">
                      -
                    </span>
                    <div className="flex-1">
                      <div className="flex h-9 items-center p-2 bg-[#fdfdfd] rounded-xl border border-[#c5c8cb]">
                        <span className="font-bodytext-sm-md text-[#909498]">
                          09/06/2025
                        </span>
                      </div>
                    </div>
                  </div>

                  <Separator className="my-2" />

                  <div className="grid grid-cols-7 gap-0">
                    {weekDays.map((day, index) => (
                      <div
                        key={`weekday-${index}`}
                        className="flex items-center justify-center h-[42px] px-2 py-2.5"
                      >
                        <span className="font-bodytext-sm-md text-[#909498]">
                          {day}
                        </span>
                      </div>
                    ))}

                    {calendarDays.map((day, index) => {
                      let className =
                        "flex flex-col items-center justify-center h-[42px] px-4 py-2";
                      let textColor = "text-[#4e5255]";

                      if (!day.currentMonth) {
                        textColor = "text-[#c5c8cb]";
                      }

                      if (day.inRange) {
                        className += " bg-[#e3daff]";
                        textColor = "text-[#7c47e6]";
                      }

                      if (day.selected) {
                        className += " relative";
                        textColor = "text-[#f4f0ff]";
                      }

                      if (day.rangeStart) {
                        className += " rounded-l-2xl";
                      }

                      if (day.rangeEnd) {
                        className += " rounded-r-2xl";
                      }

                      return (
                        <div key={`day-${index}`} className={className}>
                          {day.selected && (
                            <div className="absolute w-full h-full top-0 left-0 bg-[#7c47e6] rounded-2xl" />
                          )}
                          <span
                            className={`relative ${textColor} font-bodytext-sm-md`}
                          >
                            {day.day}
                          </span>
                        </div>
                      );
                    })}
                  </div>

                  <Separator className="my-2" />

                  <div className="flex items-center justify-end gap-3">
                    <Button
                      variant="outline"
                      className="flex-1 rounded-xl border-[#c5c8cb]"
                    >
                      <span className="font-bodytext-sm-md text-[#4e5255]">
                        Cancel
                      </span>
                    </Button>
                    <Button className="flex-1 bg-[#7c47e6] rounded-xl">
                      <span className="font-bodytext-sm-md text-[#f4f0ff]">
                        Apply
                      </span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </PopoverContent>
          </Popover>
        </div>

        <StatisticsSection />
        <DataVisualizationSection />
        <AnalyticsSection />
        <OverviewSection />
        <ContentSection />
        <InsightsSection />
      </div>

      <footer className="flex w-full items-center justify-center gap-2.5 px-[142px] py-6 border-t border-[#d9dbde]">
        <span className="font-bodytext-md-reg text-[#909498] text-center">
          socialtrack.net
        </span>
      </footer>
    </div>
  );
};
