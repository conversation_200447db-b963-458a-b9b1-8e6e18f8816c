import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Line, Doughnut } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

export const DataVisualizationSection = (): JSX.Element => {
  // Data for the interaction breakdown
  const interactionData = [
    {
      type: "Reactions",
      percentage: "48%",
      color: "bg-[#7c47e6]",
      textColor: "text-[#7c47e6]",
    },
    {
      type: "Comments",
      percentage: "29%",
      color: "bg-[#a37eff]",
      textColor: "text-[#582fa0]",
    },
    {
      type: "Shares",
      percentage: "23%",
      color: "bg-[#adb0b4]",
      textColor: "text-[#adb0b4]",
    },
  ];

  // Data for the chart dates
  const chartDates = [
    "Jul 21",
    "Jul 22",
    "Jul 23",
    "Jul 24",
    "Jul 25",
    "Jul 26",
    "Jul 27",
    "Jul 28",
    "Jul 29",
    "Jul 30",
    "Jul 31",
  ];

  // Y-axis values for the chart
  const yAxisValues = ["100K", "80K", "60K", "40K", "20K", "0"];

  // Legend data for the line chart
  const chartLegend = [
    { type: "Reactions", color: "bg-[#4f9ef0]" },
    { type: "Mentions", color: "bg-[#2bb684]" },
  ];

  // Doughnut chart data for total interactions
  const doughnutData = {
    labels: ['Reactions', 'Comments', 'Shares'],
    datasets: [
      {
        data: [48, 29, 23],
        backgroundColor: ['#7c47e6', '#a37eff', '#adb0b4'],
        borderWidth: 0,
        cutout: '70%',
      },
    ],
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            return `${context.label}: ${context.raw}%`;
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'nearest' as const,
    },
    hover: {
      mode: 'nearest' as const,
      intersect: false,
    },
    elements: {
      arc: {
        borderWidth: 2,
        hoverBorderWidth: 4,
        hoverBorderColor: '#fff',
      }
    },
  };

  // Line chart data for mentions & interactions
  const lineData = {
    labels: chartDates,
    datasets: [
      {
        label: 'Reactions',
        data: [45000, 52000, 48000, 61000, 55000, 67000, 71000, 69000, 75000, 82000, 79000],
        borderColor: '#4f9ef0',
        backgroundColor: '#4f9ef0',
        borderWidth: 2,
        fill: false,
        tension: 0.4,
      },
      {
        label: 'Mentions',
        data: [25000, 28000, 32000, 29000, 35000, 38000, 42000, 45000, 47000, 51000, 48000],
        borderColor: '#2bb684',
        backgroundColor: '#2bb684',
        borderWidth: 2,
        fill: false,
        tension: 0.4,
      },
    ],
  };

  const lineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false,
        },
        ticks: {
          color: '#4e5255',
          font: {
            size: 12,
          },
          maxRotation: 45,
        },
      },
      y: {
        display: true,
        grid: {
          color: '#f0f0f0',
        },
        ticks: {
          color: '#909498',
          font: {
            size: 12,
          },
          callback: function(value: any) {
            if (value >= 1000) {
              return (value / 1000) + 'K';
            }
            return value;
          },
        },
        min: 0,
        max: 100000,
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false,
    },
  };

  return (
    <div className="flex items-start gap-6 w-full">
      {/* Total Interactions Card */}
      <Card className="flex-1 border border-[#c5c8cb] rounded-[26px] bg-[#fdfdfd]">
        <CardContent className="flex flex-col items-start gap-6 p-6">
          <div className="w-full">
            <h3 className="font-bodytext-md-md text-[#4e5255] text-[length:var(--bodytext-md-md-font-size)] tracking-[var(--bodytext-md-md-letter-spacing)] leading-[var(--bodytext-md-md-line-height)]">
              Total Interactions
            </h3>
          </div>

          <div className="flex items-center gap-16 w-full flex-1">
            {/* Interactive Doughnut Chart */}
            <div className="relative w-[172px] h-[172px]">
              <Doughnut data={doughnutData} options={doughnutOptions} />
              <div className="flex flex-col w-[100px] h-[100px] items-center justify-center p-4 absolute top-[36px] left-[36px] bg-[#fdfdfd] rounded-[1000px] shadow-shadow-md pointer-events-none">
                <span className="font-bodytext-xs-md text-[#4e5255] text-[length:var(--bodytext-xs-md-font-size)] tracking-[var(--bodytext-xs-md-letter-spacing)] leading-[var(--bodytext-xs-md-line-height)]">
                  Total
                </span>
                <span className="font-heading-h4 text-[#141416] text-[length:var(--heading-h4-font-size)] tracking-[var(--heading-h4-letter-spacing)] leading-[var(--heading-h4-line-height)]">
                  9,821
                </span>
              </div>
            </div>

            {/* Legend and Percentages */}
            <div className="flex flex-col items-start justify-center gap-3 flex-1">
              {interactionData.map((item, index) => (
                <div
                  key={`interaction-${index}`}
                  className="flex items-center gap-3 w-full"
                >
                  <div className="flex items-center gap-3 flex-1">
                    <div className={`w-4 h-4 ${item.color} rounded`} />
                    <span className="font-bodytext-sm-md text-[#4e5255] text-[length:var(--bodytext-sm-md-font-size)] tracking-[var(--bodytext-sm-md-letter-spacing)] leading-[var(--bodytext-sm-md-line-height)]">
                      {item.type}
                    </span>
                  </div>
                  <span
                    className={`font-heading-h4 ${item.textColor} text-[length:var(--heading-h4-font-size)] tracking-[var(--heading-h4-letter-spacing)] leading-[var(--heading-h4-line-height)]`}
                  >
                    {item.percentage}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Mentions & Interactions Card */}
      <Card className="flex-1 border border-[#c5c8cb] rounded-[26px] bg-[#fdfdfd]">
        <CardContent className="flex flex-col items-start gap-6 p-6">
          <div className="w-full">
            <h3 className="font-bodytext-md-md text-[#4e5255] text-[length:var(--bodytext-md-md-font-size)] tracking-[var(--bodytext-md-md-letter-spacing)] leading-[var(--bodytext-md-md-line-height)]">
              Mentions &amp; Interactions
            </h3>
          </div>

          {/* Interactive Line Chart */}
          <div className="h-[241px] w-full">
            <Line data={lineData} options={lineOptions} />
          </div>

          {/* Chart Legend */}
          <div className="flex flex-wrap items-start gap-[16px_16px] px-4 py-0 w-full">
            {chartLegend.map((item, index) => (
              <div
                key={`legend-${index}`}
                className="inline-flex items-center gap-2"
              >
                <div className="inline-flex items-center gap-1">
                  <div className={`w-4 h-4 ${item.color} rounded`} />
                  <span className="font-bodytext-sm-md text-[#8c97a7] text-[length:var(--bodytext-sm-md-font-size)] tracking-[var(--bodytext-sm-md-letter-spacing)] leading-[var(--bodytext-sm-md-line-height)]">
                    {item.type}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
