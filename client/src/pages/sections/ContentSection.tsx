import React from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

export const ContentSection = (): JSX.Element => {
  // Data for gender distribution
  const genderData = [
    {
      label: "Male",
      percentage: "48%",
      color: "#7c47e6",
      bgColor: "#8f5cff",
      textColor: "#fdfdfd",
    },
    {
      label: "Female",
      percentage: "29%",
      color: "#a37eff",
      bgColor: "#e2daff",
      textColor: "#5a18bf",
    },
    {
      label: "Other",
      percentage: "23%",
      color: "#adb0b4",
      bgColor: "#14161d",
      textColor: "#fdfdfd",
    },
  ];

  // Data for relationship status
  const relationshipData = [
    { label: "Single", percentage: "48%", color: "#582fa0" },
    { label: "Dating", percentage: "12%", color: "#7c47e6" },
    { label: "Marriged", percentage: "28%", color: "#a37eff" },
    { label: "Other", percentage: "12%", color: "#adb0b4" },
  ];

  // Data for cities
  const cityData = [
    { name: "Ho Chi Minh City, Vietnam", value: 100 },
    { name: "Hanoi, Vietnam", value: 85 },
    { name: "Bien Hoa, Dong Nai Province, Vietnam", value: 75 },
    { name: "Can Tho, Vietnam", value: 65 },
    { name: "Ben Tre Province, Vietnam", value: 55 },
  ];

  // City horizontal bar chart data
  const cityChartData = {
    labels: cityData.map(city => city.name.split(', ')[0]), // Short names
    datasets: [
      {
        label: 'Users',
        data: cityData.map(city => city.value),
        backgroundColor: '#8f5cff',
        borderRadius: 4,
        barThickness: 20,
      },
    ],
  };

  const cityChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y' as const,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        displayColors: false,
        callbacks: {
          label: function(context: any) {
            return `Users: ${context.raw}%`;
          },
          title: function(context: any) {
            const fullName = cityData[context[0].dataIndex].name;
            return fullName;
          }
        }
      }
    },
    interaction: {
      intersect: true,
      mode: 'point' as const,
    },
    hover: {
      mode: 'point' as const,
      intersect: true,
    },
    elements: {
      bar: {
        borderWidth: 0,
        hoverBorderWidth: 2,
        hoverBorderColor: '#fff',
        hoverBackgroundColor: '#a37eff',
      }
    },
    scales: {
      x: {
        display: false,
        grid: {
          display: false,
        },
        min: 0,
        max: 100,
      },
      y: {
        display: true,
        grid: {
          display: false,
        },
        ticks: {
          color: '#515667',
          font: {
            size: 12,
          },
        },
      },
    },
  };

  // Data for age chart bars
  const ageBarData = [
    {
      male: { height: "h-[45px]", top: "top-[164px]", left: "left-[35px]" },
      female: { height: "h-[111px]", top: "top-[98px]", left: "left-[59px]" },
    },
    {
      male: { height: "h-[126px]", top: "top-[83px]", left: "left-[35px]" },
      female: { height: "h-[87px]", top: "top-[122px]", left: "left-[59px]" },
    },
    {
      male: { height: "h-[15px]", top: "top-[194px]", left: "left-9" },
      female: { height: "h-[71px]", top: "top-[138px]", left: "left-[60px]" },
    },
    {
      male: { height: "h-[147px]", top: "top-[62px]", left: "left-9" },
      female: { height: "h-[107px]", top: "top-[102px]", left: "left-[60px]" },
    },
    {
      male: { height: "h-20", top: "top-[129px]", left: "left-[35px]" },
      female: { height: "h-44", top: "top-[33px]", left: "left-[59px]" },
    },
    {
      male: { height: "h-[142px]", top: "top-[67px]", left: "left-[35px]" },
      female: { height: "h-[26px]", top: "top-[183px]", left: "left-[59px]" },
    },
  ];

  // Age ranges for x-axis
  const ageRanges = [
    "<18",
    "18-24",
    "25-34",
    "35-44",
    "44-54",
    "55-64",
    "Unknown",
  ];

  // Percentage scales for y-axis
  const percentageScales = ["60%", "50%", "40%", "30%", "20%", "10%"];

  // Gender doughnut chart data
  const genderChartData = {
    labels: ['Male', 'Female', 'Other'],
    datasets: [
      {
        data: [48, 29, 23],
        backgroundColor: ['#7c47e6', '#a37eff', '#adb0b4'],
        borderWidth: 0,
        cutout: '60%',
      },
    ],
  };

  // Relationship status doughnut chart data  
  const relationshipChartData = {
    labels: ['Single', 'Dating', 'Married', 'Other'],
    datasets: [
      {
        data: [48, 12, 28, 12],
        backgroundColor: ['#582fa0', '#7c47e6', '#a37eff', '#adb0b4'],
        borderWidth: 0,
        cutout: '60%',
      },
    ],
  };

  // Age demographics bar chart data
  const ageChartData = {
    labels: ageRanges,
    datasets: [
      {
        label: 'Male',
        data: [15, 35, 5, 40, 25, 38, 8],
        backgroundColor: '#8f5cff',
        borderRadius: 4,
      },
      {
        label: 'Female', 
        data: [30, 25, 20, 28, 45, 10, 15],
        backgroundColor: '#e2daff',
        borderRadius: 4,
      },
    ],
  };

  // Gender chart options
  const genderChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            return `${context.label}: ${context.raw}%`;
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'nearest' as const,
    },
    hover: {
      mode: 'nearest' as const,
      intersect: false,
    },
    elements: {
      arc: {
        borderWidth: 2,
        hoverBorderWidth: 4,
        hoverBorderColor: '#fff',
      }
    },
  };

  // Relationship chart options
  const relationshipChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            return `${context.label}: ${context.raw}%`;
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'nearest' as const,
    },
    hover: {
      mode: 'nearest' as const,
      intersect: false,
    },
    elements: {
      arc: {
        borderWidth: 2,
        hoverBorderWidth: 4,
        hoverBorderColor: '#fff',
      }
    },
  };

  const ageChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        mode: 'index' as const,
        intersect: false,
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${context.raw}%`;
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
    hover: {
      mode: 'index' as const,
      intersect: false,
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false,
        },
        ticks: {
          color: '#4e5255',
          font: {
            size: 12,
          },
        },
      },
      y: {
        display: true,
        grid: {
          color: '#f0f0f0',
        },
        ticks: {
          color: '#909498',
          font: {
            size: 12,
          },
          callback: function(value: any) {
            return value + '%';
          },
        },
        min: 0,
        max: 50,
      },
    },
  };

  return (
    <section className="flex flex-col items-start gap-6 w-full">
      {/* Top row with Age and Gender cards */}
      <div className="flex items-start gap-6 w-full">
        {/* Age Card */}
        <Card className="flex-1 bg-[#fdfdfd] border-[#c5c8cb] rounded-2xl overflow-hidden">
          <CardContent className="flex flex-col items-start gap-6 p-4 h-[352px]">
            <div className="flex items-center justify-between w-full">
              <h3 className="font-bodytext-md-md text-[#20232c]">Age</h3>

              <div className="inline-flex items-center gap-1">
                {/* Gender filter badges */}
                <Badge className="gap-2 px-2 py-1 bg-[#e2daff] text-[#5a18bf] border-[#e1e2e3] rounded-[1000px]">
                  <img
                    className="w-5 h-5"
                    alt="Women line"
                    src="/figmaAssets/women-line.svg"
                  />
                  <span className="font-inter-body-s text-[#5a18bf]">
                    Female
                  </span>
                </Badge>

                <Badge className="gap-2 px-2 py-1 bg-[#8f5cff] text-[#fdfdfd] border-[#e1e2e3] rounded-[1000px]">
                  <img
                    className="w-5 h-5"
                    alt="Men line"
                    src="/figmaAssets/men-line.svg"
                  />
                  <span className="font-inter-body-s text-[#fdfdfd]">Male</span>
                </Badge>

                <Badge className="gap-2 px-2 py-1 bg-[#14161d] text-[#fdfdfd] border-[#e1e2e3] rounded-[1000px]">
                  <img
                    className="w-5 h-5"
                    alt="Gender"
                    src="/figmaAssets/gender.svg"
                  />
                  <span className="font-inter-body-s text-[#fdfdfd]">
                    Unknown
                  </span>
                </Badge>

                <Badge className="gap-2 px-2 py-1 bg-[#f0f0f0] text-[#20232c] border-[#e1e2e3] rounded-[1000px]">
                  <img
                    className="w-5 h-5"
                    alt="Eye line"
                    src="/figmaAssets/eye-line.svg"
                  />
                  <span className="font-inter-body-s text-[#20232c]">
                    View all
                  </span>
                </Badge>
              </div>
            </div>

            {/* Interactive Age Bar Chart */}
            <div className="w-full flex-1">
              <Bar data={ageChartData} options={ageChartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Gender Card */}
        <Card className="flex-1 bg-[#fdfdfd] border-[#c5c8cb] rounded-2xl overflow-hidden">
          <CardContent className="flex flex-col items-center justify-between h-[352px] px-6 py-4">
            <div className="flex flex-col items-start gap-6 w-full">
              <div className="flex flex-col items-start gap-2 w-full">
                <div className="flex items-center gap-2.5 w-full">
                  <div className="flex items-center gap-2 flex-1">
                    <h3 className="flex-1 font-bodytext-md-md text-[#515667]">
                      Gender
                    </h3>
                  </div>
                </div>
              </div>
            </div>

            {/* Interactive Gender Doughnut Chart */}
            <div className="relative w-[172px] h-[172px]">
              <Doughnut data={genderChartData} options={genderChartOptions} />
              <div className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none">
                <span className="font-bodytext-xs-md text-[#4e5255] text-center">
                  Total
                </span>
                <span className="font-heading-h4 text-[#141416] text-center">
                  9,821
                </span>
              </div>
            </div>

            {/* Legend */}
            <div className="w-[299px] flex items-center justify-between">
              {genderData.map((item, index) => (
                <div
                  key={`gender-legend-${index}`}
                  className="inline-flex gap-1 items-center"
                >
                  <div
                    className={`w-3 h-3 bg-[${item.color}] rounded`}
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="font-bodytext-xs-md text-[#6b7183] whitespace-nowrap">
                    {item.label}
                  </span>
                  <span
                    className={`font-bodytext-md-semibold text-[${item.color}] whitespace-nowrap`}
                    style={{ color: item.color }}
                  >
                    {item.percentage}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bottom row with City and Relationships cards */}
      <div className="flex items-start gap-6 w-full">
        {/* City Card */}
        <Card className="flex-1 bg-[#fdfdfd] border-[#c5c8cb] rounded-2xl overflow-hidden">
          <CardContent className="flex flex-col items-start gap-6 p-4 h-[352px]">
            <h3 className="font-bodytext-md-md text-[#20232c]">City</h3>

            {/* Interactive City Horizontal Bar Chart */}
            <div className="w-full flex-1">
              <Bar data={cityChartData} options={cityChartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Relationships Card */}
        <Card className="flex-1 bg-[#fdfdfd] border-[#c5c8cb] rounded-2xl overflow-hidden">
          <CardContent className="flex flex-col items-center justify-between h-[352px] px-6 py-4">
            <div className="flex flex-col items-start gap-6 w-full">
              <div className="flex flex-col items-start gap-2 w-full">
                <div className="flex items-center gap-2.5 w-full">
                  <div className="flex items-center gap-2 flex-1">
                    <h3 className="flex-1 font-bodytext-md-md text-[#20232c]">
                      Relationships
                    </h3>
                  </div>
                </div>
              </div>
            </div>

            {/* Interactive Relationship Doughnut Chart */}
            <div className="relative w-[172px] h-[172px]">
              <Doughnut data={relationshipChartData} options={relationshipChartOptions} />
              <div className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none">
                <span className="font-bodytext-xs-md text-[#4e5255] text-center">
                  Total
                </span>
                <span className="font-heading-h4 text-[#141416] text-center">
                  9,821
                </span>
              </div>
            </div>

            {/* Legend */}
            <div className="flex flex-wrap items-start justify-center gap-[8px_16px] w-full">
              {relationshipData.map((item, index) => (
                <div
                  key={`relationship-legend-${index}`}
                  className="inline-flex items-center gap-1.5"
                >
                  <div
                    className={`w-3 h-3 rounded`}
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="font-bodytext-xs-md text-[#6b7183] whitespace-nowrap">
                    {item.label}
                  </span>
                  <span
                    className="font-bodytext-sm-semibold whitespace-nowrap"
                    style={{ color: item.color }}
                  >
                    {item.percentage}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};
