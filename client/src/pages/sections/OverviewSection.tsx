import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";

export const OverviewSection = (): JSX.Element => {
  // Data for trending hashtags
  const trendingHashtags = [
    { name: "#vemaybaygiare", mentions: "1,234 mentions" },
    { name: "#vietnamairlines", mentions: "1,234 mentions" },
    { name: "#vietjetairs", mentions: "1,234 mentions" },
    { name: "#bambooairways", mentions: "1,234 mentions" },
    { name: "#sanvemaybay", mentions: "1,234 mentions" },
    { name: "#businessclass", mentions: "1,234 mentions" },
    { name: "#vebay<PERSON><PERSON><PERSON>", mentions: "1,234 mentions" },
    { name: "#hangbaychatluong", mentions: "1,234 mentions" },
  ];

  // Data for keyword cloud
  const keywordCloudItems = [
    {
      text: "DuLị<PERSON>",
      className: "absolute top-0 left-[66px] font-heading-h4 text-[#a37eff]",
    },
    {
      text: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      className: "absolute top-8 left-0 font-heading-h4 text-[#a37eff]",
    },
    {
      text: "Du<PERSON>ịchThếGiới",
      className:
        "absolute top-[190px] left-[95px] font-title-md text-[#c5c8cb]",
    },
    {
      text: "TrảiNghiệmBay",
      className: "absolute top-[213px] left-24 font-heading-h4 text-[#a37eff]",
    },
    {
      text: "ChuyếnBay",
      className: "absolute top-[152px] left-60 font-heading-h4 text-[#a37eff]",
    },
    {
      text: " HànhTrình",
      className: "absolute top-[7px] left-[285px] font-title-md text-[#c5c8cb]",
    },
    {
      text: "BayĐếnMơƯớc",
      className:
        "absolute top-[152px] left-[58px] font-heading-h4 text-[#a37eff]",
    },
    {
      text: "VietnamAirlines",
      className: "absolute top-[85px] left-0 font-heading-h4 text-[#a37eff]",
    },
    {
      text: "VéMáyBay",
      className:
        "absolute top-[29px] left-[315px] font-heading-h4 text-[#a37eff]",
    },
    {
      text: "VietJetAir",
      className: "absolute top-0 left-[166px] font-heading-h4 text-[#a37eff]",
    },
    {
      text: "BambooAirways",
      className: "absolute top-7 left-[120px] font-heading-h4 text-[#a37eff]",
    },
    {
      text: "KhámPhá",
      className: "absolute top-2 left-[378px] font-title-md text-[#c5c8cb]",
    },
    {
      text: "BayCùngBạnBè",
      className:
        "absolute top-[85px] left-[392px] font-heading-h4 text-[#a37eff]",
    },
    {
      text: "ChuyếnBayAnToàn",
      className:
        "absolute top-[114px] left-[5px] font-heading-h3 text-[#7c47e6]",
    },
    {
      text: "ChuyếnBayAnToàn",
      className:
        "absolute top-[184px] left-[222px] font-heading-h4 text-[#a37eff]",
    },
    {
      text: "VéGiáRẻ",
      className:
        "absolute top-[62px] left-[189px] font-heading-h1 text-[#7c47e6]",
    },
    {
      text: "ĐặtVéOnline",
      className:
        "absolute top-[114px] left-[305px] font-heading-h3 text-[#7c47e6]",
    },
    {
      text: "ChuyếnĐi",
      className:
        "absolute top-[55px] left-[69px] font-heading-h4 text-[#a37eff]",
    },
    {
      text: "HạngVéMáyBay",
      className: "absolute top-40 left-[380px] font-title-md text-[#c5c8cb]",
    },
    {
      text: "DuLịchViệtNam",
      className:
        "absolute top-[61px] left-[393px] font-title-md text-[#c5c8cb]",
    },
    {
      text: "ChuyếnĐi",
      className: "absolute top-32 left-[506px] font-title-md text-[#c5c8cb]",
    },
  ];

  return (
    <div className="flex items-start gap-6 relative self-stretch w-full">
      {/* Trending Hashtag Card */}
      <Card className="flex flex-col w-[335px] items-start gap-6 p-6 bg-[#fdfdfd] rounded-[26px] border border-solid border-[#c5c8cb]">
        <CardHeader className="flex items-start justify-between p-0 pb-2 w-full border-b border-[#c5c8cb]">
          <div className="inline-flex flex-col items-start justify-center gap-1">
            <h3 className="font-bodytext-md-md text-[#4e5255]">
              Trending Hashtag
            </h3>
          </div>
        </CardHeader>
        <CardContent className="p-0 space-y-6 w-full">
          {trendingHashtags.map((hashtag, index) => (
            <div
              key={index}
              className="flex items-start justify-between w-full"
            >
              <div className="font-bodytext-sm-md text-[#141416]">
                {hashtag.name}
              </div>
              <div className="font-bodytext-xs-reg text-[#909498]">
                {hashtag.mentions}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Relevant Keyword Cloud Card */}
      <Card className="flex flex-col items-start gap-6 px-10 py-6 flex-1 bg-[#fdfdfd] rounded-[26px] border border-solid border-[#c5c8cb]">
        <CardHeader className="flex items-start justify-between p-0 pb-2 w-full border-b border-[#c5c8cb]">
          <div className="inline-flex flex-col items-start justify-center gap-1">
            <h3 className="font-bodytext-md-md text-[#4e5255]">
              Relevant Keyword Cloud
            </h3>
          </div>
        </CardHeader>
        <CardContent className="p-0 flex flex-col items-center justify-center gap-2.5 w-full h-full">
          <div className="relative w-[637.5px] h-[245px]">
            <div className="relative w-[596px] h-[245px]">
              {keywordCloudItems.map((item, index) => (
                <div key={index} className={item.className}>
                  {item.text}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
