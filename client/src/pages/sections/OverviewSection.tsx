import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader } from "@/components/ui/card";
import { WordCloudComponent } from "@/components/ui/word-cloud";

export const OverviewSection = (): JSX.Element => {
  // Data for trending hashtags
  const trendingHashtags = [
    { name: "#vemaybaygiare", mentions: "1,234 mentions" },
    { name: "#vietnamairlines", mentions: "1,234 mentions" },
    { name: "#vietjetairs", mentions: "1,234 mentions" },
    { name: "#bambooairways", mentions: "1,234 mentions" },
    { name: "#sanvemaybay", mentions: "1,234 mentions" },
    { name: "#businessclass", mentions: "1,234 mentions" },
    { name: "#vebaykhuhoi", mentions: "1,234 mentions" },
    { name: "#hangbaychatluong", mentions: "1,234 mentions" },
  ];

  // Data for keyword cloud - converted to react-d3-cloud format
  const keywordCloudData = [
    { text: "VéGiáRẻ", value: 100 },
    { text: "ChuyếnBayAn<PERSON><PERSON><PERSON><PERSON>", value: 80 },
    { text: "ĐặtVéOnline", value: 75 },
    { text: "Du<PERSON>ị<PERSON>", value: 65 },
    { text: "VietnamAirlines", value: 60 },
    { text: "VéMáyBay", value: 55 },
    { text: "ChuyếnBay", value: 50 },
    { text: "VietJetAir", value: 45 },
    { text: "BambooAirways", value: 40 },
    { text: "ChuyếnĐi", value: 35 },
    { text: "TrảiNghiệmBay", value: 30 },
    { text: "BayĐếnMơƯớc", value: 25 },
    { text: "BayCùngBạnBè", value: 20 },
    { text: "DuLịchThếGiới", value: 18 },
    { text: "HànhTrình", value: 15 },
    { text: "KhámPhá", value: 12 },
    { text: "HạngVéMáyBay", value: 10 },
    { text: "DuLịchViệtNam", value: 8 },
  ];

  return (
    <div className="flex items-start gap-6 relative self-stretch w-full">
      {/* Trending Hashtag Card */}
      <Card className="flex flex-col w-[335px] items-start gap-6 p-6 bg-[#fdfdfd] rounded-[26px] border border-solid border-[#c5c8cb] h-[540px]">
        <CardHeader className="flex items-start justify-between p-0 pb-2 w-full border-b border-[#c5c8cb]">
          <div className="inline-flex flex-col items-start justify-center gap-1">
            <h3 className="font-bodytext-md-md text-[#4e5255]">
              Trending Hashtag
            </h3>
          </div>
        </CardHeader>
        <CardContent className="p-0 space-y-6 w-full flex-1 overflow-auto pr-4">
          {trendingHashtags.map((hashtag, index) => (
            <div
              key={index}
              className="flex items-start justify-between w-full"
            >
              <div className="font-bodytext-sm-md text-[#141416]">
                {hashtag.name}
              </div>
              <div className="font-bodytext-xs-reg text-[#909498]">
                {hashtag.mentions}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Relevant Keyword Cloud Card */}
      <Card className="flex flex-col items-start gap-6 px-10 py-6 flex-1 bg-[#fdfdfd] rounded-[26px] border border-solid border-[#c5c8cb] h-[540px]">
        <CardHeader className="flex items-start justify-between p-0 pb-2 w-full border-b border-[#c5c8cb]">
          <div className="inline-flex flex-col items-start justify-center gap-1">
            <h3 className="font-bodytext-md-md text-[#4e5255]">
              Relevant Keyword Cloud
            </h3>
          </div>
        </CardHeader>
        <CardContent className="p-0 flex flex-col items-center justify-center gap-2.5 w-full h-full">
          <div className="flex justify-center items-center w-full h-full">
            <WordCloudComponent
              data={keywordCloudData}
              width={600}
              height={245}
              fontSizeMapper={(word) => Math.log2(word.value) * 8 + 12}
              rotate={() => (Math.random() - 0.5) * 60}
              className="word-cloud-overview"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
