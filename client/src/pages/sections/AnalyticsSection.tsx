import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

export const AnalyticsSection = (): JSX.Element => {
  // Data for sentiment analysis
  const sentimentData = [
    {
      label: "Positive",
      percentage: "48%",
      color: "bg-[#2bb684] text-[#2bb684]",
    },
    {
      label: "Negative",
      percentage: "23%",
      color: "bg-[#f84242] text-[#f84242]",
    },
    {
      label: "Neutral",
      percentage: "29%",
      color: "bg-[#c5c8cb] text-[#909498]",
    },
  ];

  // Data for chart dates
  const chartDates = [
    "Jul 21",
    "Jul 22",
    "Jul 23",
    "Jul 24",
    "Jul 25",
    "Jul 26",
    "Jul 27",
    "Jul 28",
    "Jul 29",
    "Jul 30",
    "Jul 31",
  ];

  // Y-axis values
  const yAxisValues = ["100K", "80K", "60K", "40K", "20K", "0"];

  // Sentiment analysis doughnut chart data
  const sentimentChartData = {
    labels: ['Positive', 'Negative', 'Neutral'],
    datasets: [
      {
        data: [48, 23, 29],
        backgroundColor: ['#2bb684', '#f84242', '#c5c8cb'],
        borderWidth: 0,
        cutout: '60%',
      },
    ],
  };

  const sentimentChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            return `${context.label}: ${context.raw}%`;
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'nearest' as const,
    },
    hover: {
      mode: 'nearest' as const,
      intersect: false,
    },
    elements: {
      arc: {
        borderWidth: 2,
        hoverBorderWidth: 4,
        hoverBorderColor: '#fff',
      }
    },
  };

  // Bar chart data for sentiment over time
  const sentimentBarData = {
    labels: chartDates,
    datasets: [
      {
        label: 'Positive',
        data: [42000, 45000, 48000, 51000, 49000, 53000, 55000, 52000, 58000, 61000, 59000],
        backgroundColor: '#2bb684',
        borderRadius: 4,
      },
      {
        label: 'Negative', 
        data: [18000, 21000, 19000, 23000, 22000, 25000, 24000, 26000, 27000, 24000, 26000],
        backgroundColor: '#f84242',
        borderRadius: 4,
      },
      {
        label: 'Neutral',
        data: [25000, 28000, 27000, 30000, 32000, 29000, 33000, 31000, 34000, 36000, 35000],
        backgroundColor: '#c5c8cb',
        borderRadius: 4,
      },
    ],
  };

  const sentimentBarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false,
        },
        ticks: {
          color: '#4e5255',
          font: {
            size: 12,
          },
          maxRotation: 45,
        },
      },
      y: {
        display: true,
        grid: {
          color: '#f0f0f0',
        },
        ticks: {
          color: '#909498',
          font: {
            size: 12,
          },
          callback: function(value: any) {
            if (value >= 1000) {
              return (value / 1000) + 'K';
            }
            return value;
          },
        },
        min: 0,
        max: 70000,
      },
    },
  };

  return (
    <section className="flex items-start gap-6 relative self-stretch w-full">
      <Card className="flex-1 border border-solid border-[#c5c8cb] rounded-[26px] bg-[#fdfdfd] overflow-hidden">
        <CardContent className="flex flex-col items-start gap-4 p-6">
          <div className="flex items-start justify-between w-full">
            <div className="flex flex-col items-start justify-center gap-1">
              <h3 className="font-bodytext-md-md font-[number:var(--bodytext-md-md-font-weight)] text-[#4e5255] text-[length:var(--bodytext-md-md-font-size)] tracking-[var(--bodytext-md-md-letter-spacing)] leading-[var(--bodytext-md-md-line-height)] [font-style:var(--bodytext-md-md-font-style)]">
                Sentiment Analysis
              </h3>
            </div>
          </div>

          <div className="flex h-[235px] items-center justify-center gap-16 w-full">
            <div className="flex h-[198px] items-center justify-center gap-16 flex-1">
              {/* Interactive Sentiment Doughnut Chart */}
              <div className="relative w-[188px] h-[188px]">
                <Doughnut data={sentimentChartData} options={sentimentChartOptions} />
              </div>

              {/* Legend */}
              <div className="flex flex-col items-start justify-center gap-2 flex-1">
                {sentimentData.map((item, index) => (
                  <div key={index} className="flex items-center gap-3 w-full">
                    <div className="flex items-center gap-3 flex-1 self-stretch">
                      <div
                        className={`relative w-4 h-4 ${item.color.split(" ")[0]} rounded`}
                      />
                      <div className="relative w-fit font-bodytext-sm-md font-[number:var(--bodytext-sm-md-font-weight)] text-[#4e5255] text-[length:var(--bodytext-sm-md-font-size)] tracking-[var(--bodytext-sm-md-letter-spacing)] leading-[var(--bodytext-sm-md-line-height)] whitespace-nowrap [font-style:var(--bodytext-sm-md-font-style)]">
                        {item.label}
                      </div>
                    </div>
                    <div
                      className={`font-[number:var(--heading-h4-font-weight)] ${item.color.split(" ")[1]} text-[length:var(--heading-h4-font-size)] tracking-[var(--heading-h4-letter-spacing)] leading-[var(--heading-h4-line-height)] relative w-fit mt-[-1.00px] font-heading-h4 whitespace-nowrap [font-style:var(--heading-h4-font-style)]`}
                    >
                      {item.percentage}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Interactive Sentiment Bar Chart */}
            <div className="flex w-[552px] h-[235px] items-center gap-3 relative">
              <div className="w-full h-full">
                <Bar data={sentimentBarData} options={sentimentBarOptions} />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </section>
  );
};
