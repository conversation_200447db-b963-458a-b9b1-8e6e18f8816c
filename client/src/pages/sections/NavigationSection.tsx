import React from "react";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";

export const NavigationSection = (): JSX.Element => {
  return (
    <header className="w-full h-20 flex items-center justify-between px-8 md:px-[142px] py-4 bg-[#fdfdfd] shadow-soft-shadow-xs">
      <div className="flex-shrink-0">
        <img
          className="w-[189px] h-[52px] object-cover"
          alt="Socialtracking.net logo"
          src="/figmaAssets/socialtracking-net-1.png"
        />
      </div>

      <h1 className="font-heading-h3 font-[number:var(--heading-h3-font-weight)] text-[#141416] text-[length:var(--heading-h3-font-size)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] [font-style:var(--heading-h3-font-style)]">
        Dashboard
      </h1>

      <div className="flex items-center gap-2">
        <Avatar className="w-8 h-8 border border-solid">
          <AvatarImage src="/figmaAssets/img.png" alt="User profile picture" />
          <AvatarFallback>DS</AvatarFallback>
        </Avatar>

        <div className="flex flex-col items-start">
          <span className="font-bodytext-sm-semibold font-[number:var(--bodytext-sm-semibold-font-weight)] text-[#141416] text-[length:var(--bodytext-sm-semibold-font-size)] tracking-[var(--bodytext-sm-semibold-letter-spacing)] leading-[var(--bodytext-sm-semibold-line-height)] [font-style:var(--bodytext-sm-semibold-font-style)]">
            Darrell Steward
          </span>
        </div>
      </div>
    </header>
  );
};
