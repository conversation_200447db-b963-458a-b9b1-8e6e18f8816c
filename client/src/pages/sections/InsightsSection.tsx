import React from "react";
import {
  Ava<PERSON>,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
} from "@/components/ui/pagination";
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Doughnut } from "react-chartjs-2";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

export const InsightsSection = (): JSX.Element => {
  // Data for mentioned posts
  const mentionedPosts = [
    {
      id: 1,
      author: "<PERSON> Henry",
      avatar: "/figmaAssets/img-1.png",
      time: "5 giờ trước",
      platform: "Fanpage",
      sentiment: "normal",
      sentimentIcon: "/figmaAssets/emotion-normal-line.svg",
      content:
        "Vietnam Airlines are making waves in the aviation industry! With exciting new routes and innovative services, these airlines are set to redefine travel in Vietnam. Whether you're flying for business or leisure, there's a perfect option for everyone. Stay tuned for updates on their latest offerings!",
      likes: 388,
      comments: 399,
      shares: 123,
      highlightPosition: { left: "3.5px", width: "114px" },
    },
    {
      id: 2,
      author: "Kathryn Murphy",
      avatar: "/figmaAssets/img-2.png",
      time: "5 giờ trước",
      platform: "Fanpage",
      sentiment: "unhappy",
      sentimentIcon: "/figmaAssets/emotion-unhappy-line.svg",
      content:
        "Have you heard about the latest from Bamboo Airways? These carriers are transforming the skies with fresh routes and enhanced passenger experiences. From budget-friendly options to premium services, they cater to all travelers. Keep an eye out for their upcoming announcements!",
      likes: 388,
      comments: 399,
      shares: 123,
      highlightPosition: { left: "272px", width: "116px" },
    },
    {
      id: 3,
      author: "Robert Fox",
      avatar: "/figmaAssets/img-3.png",
      time: "5 giờ trước",
      platform: "Fanpage",
      sentiment: "happy",
      sentimentIcon: "/figmaAssets/emotion-happy-line.svg",
      content:
        "Vietjet Air are leading the charge in Vietnam's aviation scene! With new destinations and improved services, they're making air travel more accessible and enjoyable. Whether you're planning a getaway or a business trip, these airlines have something for everyone!",
      likes: 388,
      comments: 399,
      shares: 123,
      highlightPosition: { left: "3px", width: "75px" },
    },
    {
      id: 4,
      author: "Jane Cooper",
      avatar: "/figmaAssets/img-8.png",
      time: "5 giờ trước",
      platform: "Fanpage",
      sentiment: "happy",
      sentimentIcon: "/figmaAssets/emotion-happy-line.svg",
      content:
        "Exciting news from Vietjet Air! These airlines are revolutionizing air travel in Vietnam with new routes and innovative services. Whether you're traveling for work or pleasure, there's an option that fits your needs perfectly. Stay tuned for more updates!",
      likes: 388,
      comments: 399,
      shares: 123,
      highlightPosition: { left: "149px", width: "72px" },
    },
  ];

  // Data for provider posts
  const providerPosts = [
    {
      id: 1,
      author: "Darrell Steward",
      avatar: "/figmaAssets/img-5.png",
      time: "5 giờ trước",
      platform: "Fanpage",
      sentiment: "unhappy",
      sentimentIcon: "/figmaAssets/emotion-unhappy-line.svg",
      content:
        "Vietnam Airlines pilots are advocating for better profit-sharing agreements, leading to a significant strike that could disrupt thousands of flights.",
      likes: 388,
      comments: 399,
      shares: 123,
      highlightPosition: { left: "3.5px", width: "114px" },
    },
    {
      id: 2,
      author: "Albert Flores",
      avatar: "/figmaAssets/img-6.png",
      time: "5 giờ trước",
      platform: "Fanpage",
      sentiment: "normal",
      sentimentIcon: "/figmaAssets/emotion-normal-line.svg",
      content:
        "Bamboo Airways pilots have initiated a strike to push for profit-sharing, potentially impacting numerous scheduled flights.",
      likes: 388,
      comments: 399,
      shares: 123,
      highlightPosition: { left: "17px", width: "116px" },
    },
    {
      id: 3,
      author: "Ralph Edwards",
      avatar: "/figmaAssets/img-7.png",
      time: "5 giờ trước",
      platform: "Fanpage",
      sentiment: "happy",
      sentimentIcon: "/figmaAssets/emotion-happy-line.svg",
      content:
        "Vietjet Air pilots are striking to demand fair profit-sharing, which may result in the cancellation of many flights.",
      likes: 388,
      comments: 399,
      shares: 123,
      highlightPosition: { left: "3px", width: "75px" },
    },
    {
      id: 4,
      author: "Jane Cooper",
      avatar: "/figmaAssets/img-8.png",
      time: "5 giờ trước",
      platform: "Fanpage",
      sentiment: "happy",
      sentimentIcon: "/figmaAssets/emotion-happy-line.svg",
      content:
        "Exciting news from Vietjet Air! These airlines are revolutionizing air travel in Vietnam with new routes and innovative services. Whether you're traveling for work or pleasure, there's an option that fits your needs perfectly. Stay tuned for more updates!",
      likes: 388,
      comments: 399,
      shares: 123,
      highlightPosition: { left: "149px", width: "72px" },
    },
  ];

  // Chart data
  const chartData = [
    {
      label: "Provider",
      percentage: "48%",
      color: "bg-[#7c47e6]",
      textColor: "text-[#7c47e6]",
    },
    {
      label: "Seeker",
      percentage: "29%",
      color: "bg-[#a37eff]",
      textColor: "text-[#a37eff]",
    },
    {
      label: "Neutral",
      percentage: "23%",
      color: "bg-[#adb0b4]",
      textColor: "text-[#adb0b4]",
    },
  ];

  // User Intent Classification doughnut chart data
  const intentChartData = {
    labels: ['Provider', 'Seeker', 'Neutral'],
    datasets: [
      {
        data: [48, 29, 23],
        backgroundColor: ['#7c47e6', '#a37eff', '#adb0b4'],
        borderWidth: 0,
        cutout: '60%',
      },
    ],
  };

  const intentChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            return `${context.label}: ${context.raw}%`;
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'nearest' as const,
    },
    hover: {
      mode: 'nearest' as const,
      intersect: false,
    },
    elements: {
      arc: {
        borderWidth: 2,
        hoverBorderWidth: 4,
        hoverBorderColor: '#fff',
      }
    },
  };

  // Pagination numbers
  const paginationNumbers = [1, 2, 3, 4, 8, 9, 10];

  // Render a social media post card
  const renderPostCard = (post: any) => (
    <div
      key={post.id}
      className="flex flex-col items-start gap-2.5 relative self-stretch w-full"
    >
      <Card className="flex flex-col items-start gap-4 p-4 w-full bg-[#fdfdfd] border-[#c5c8cb]">
        <CardContent className="p-0 w-full space-y-4">
          <div className="flex items-start justify-between w-full">
            <div className="flex items-center gap-2">
              <Avatar className="w-10 h-10 border border-solid bg-[#fdfdfd]">
                <AvatarImage
                  src={post.avatar}
                  alt={post.author}
                  className="object-cover"
                />
                <AvatarFallback>{post.author.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className="flex flex-col items-start gap-1">
                <div className="font-bodytext-sm-md text-[#141416] text-[length:var(--bodytext-sm-md-font-size)] tracking-[var(--bodytext-sm-md-letter-spacing)] leading-[var(--bodytext-sm-md-line-height)]">
                  {post.author}
                </div>
                <div className="font-bodytext-xs-reg text-[#909498] text-[length:var(--bodytext-xs-reg-font-size)] tracking-[var(--bodytext-xs-reg-letter-spacing)] leading-[var(--bodytext-xs-reg-line-height)]">
                  {post.time} • {post.platform}
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <img className="w-6 h-6" alt="Emotion" src={post.sentimentIcon} />
            </div>
          </div>

          <div className="font-bodytext-sm-reg text-[#4e5255] text-[length:var(--bodytext-sm-reg-font-size)] tracking-[var(--bodytext-sm-reg-letter-spacing)] leading-[var(--bodytext-sm-reg-line-height)]">
            {post.content}
          </div>

          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3 h-5">
              <div className="flex items-center gap-1">
                <img
                  className="w-5 h-5"
                  alt="Likes"
                  src="/figmaAssets/thumb-up-line.svg"
                />
                <span className="font-bodytext-xs-reg text-[#515667] text-[length:var(--bodytext-xs-reg-font-size)] text-center tracking-[var(--bodytext-xs-reg-letter-spacing)] leading-[var(--bodytext-xs-reg-line-height)]">
                  {post.likes}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <img
                  className="w-5 h-5"
                  alt="Comments"
                  src="/figmaAssets/chat-3-line.svg"
                />
                <span className="font-bodytext-xs-reg text-[#515667] text-[length:var(--bodytext-xs-reg-font-size)] text-center tracking-[var(--bodytext-xs-reg-letter-spacing)] leading-[var(--bodytext-xs-reg-line-height)]">
                  {post.comments}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <img
                  className="w-5 h-5"
                  alt="Shares"
                  src="/figmaAssets/share-forward-line.svg"
                />
                <span className="font-bodytext-xs-reg text-[#515667] text-[length:var(--bodytext-xs-reg-font-size)] text-center tracking-[var(--bodytext-xs-reg-letter-spacing)] leading-[var(--bodytext-xs-reg-line-height)]">
                  {post.shares}
                </span>
              </div>
            </div>
            <Button
              variant="ghost"
              className="p-1 rounded-lg flex items-center gap-1"
            >
              <span className="font-bodytext-sm-md text-[#7c47e6] text-[length:var(--bodytext-sm-md-font-size)] tracking-[var(--bodytext-sm-md-letter-spacing)] leading-[var(--bodytext-sm-md-line-height)]">
                Visit
              </span>
              <img
                className="w-5 h-5"
                alt="External link"
                src="/figmaAssets/external-link-line.svg"
              />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Highlight element */}
      <div
        className="absolute"
        style={{
          top: "75px",
          left: post.highlightPosition.left,
          width: post.highlightPosition.width,
        }}
      >
        <div className="h-4 w-full bg-[#8f5cff3d]" />
      </div>
    </div>
  );

  return (
    <section className="flex gap-6 w-full">
      {/* Left panel - Mentioned Posts */}
      <Card className="flex-1 gap-3 p-4 bg-[#fdfdfd] rounded-2xl border-[#c5c8cb]">
        <div className="font-bodytext-md-md text-[#4e5255] text-[length:var(--bodytext-md-md-font-size)] tracking-[var(--bodytext-md-md-letter-spacing)] leading-[var(--bodytext-md-md-line-height)]">
          Mentioned Posts
        </div>

        <Tabs defaultValue="top-mentions" className="w-full">
          <TabsList className="flex h-10 gap-2 w-full border-b border-[#c5c8cb] bg-transparent p-0">
            <TabsTrigger
              value="top-mentions"
              className="flex-1 rounded-none border-b-2 border-[#7c47e6] data-[state=active]:border-[#7c47e6] data-[state=active]:text-[#7c47e6] data-[state=inactive]:border-[#c5c8cb] data-[state=inactive]:text-[#4e5255] pb-2 pt-0 px-1 bg-transparent"
            >
              <div className="font-bodytext-sm-md px-2 py-1">Top Mentions</div>
            </TabsTrigger>
            <TabsTrigger
              value="top-influencers"
              className="flex-1 rounded-none border-b-2 border-[#c5c8cb] data-[state=active]:border-[#7c47e6] data-[state=active]:text-[#7c47e6] data-[state=inactive]:border-[#c5c8cb] data-[state=inactive]:text-[#4e5255] pb-2 pt-0 px-1 bg-transparent"
            >
              <div className="font-bodytext-sm-md px-2 py-1">
                Top influencers
              </div>
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value="top-mentions"
            className="mt-4 space-y-4 overflow-hidden"
          >
            {mentionedPosts.map((post) => renderPostCard(post))}
          </TabsContent>

          <TabsContent value="top-influencers" className="mt-4">
            {/* Content for top influencers tab */}
          </TabsContent>
        </Tabs>

        <Pagination className="mt-4">
          <PaginationContent className="flex items-center gap-[7px]">
            <PaginationItem>
              <Button variant="ghost" size="icon" className="p-2 rounded-xl">
                <img
                  className="w-5 h-5"
                  alt="Previous"
                  src="/figmaAssets/arrow-left-s-line.svg"
                />
              </Button>
            </PaginationItem>

            {paginationNumbers.map((num, index) => (
              <PaginationItem key={index}>
                {num === 1 ? (
                  <PaginationLink
                    className="w-5 h-5 p-1 rounded-lg border border-solid border-[#c5c8cb] font-bodytext-sm-md text-[#141416]"
                    isActive
                  >
                    {num}
                  </PaginationLink>
                ) : (
                  <PaginationLink className="w-5 h-5 p-1 rounded-lg font-bodytext-sm-md text-[#4e5255]">
                    {num}
                  </PaginationLink>
                )}
              </PaginationItem>
            ))}

            <PaginationItem>
              <PaginationEllipsis className="w-5 h-5 p-1 rounded-lg font-bodytext-sm-md text-[#4e5255]" />
            </PaginationItem>

            <PaginationItem>
              <Button variant="ghost" size="icon" className="p-2 rounded-xl">
                <img
                  className="w-5 h-5"
                  alt="Next"
                  src="/figmaAssets/arrow-right-s-line.svg"
                />
              </Button>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </Card>

      {/* Right panel - User Intent Classification */}
      <Card className="flex-1 gap-6 px-6 py-4 bg-[#fdfdfd] rounded-2xl border-[#c5c8cb]">
        <div className="font-bodytext-md-md text-[#4e5255] text-[length:var(--bodytext-md-md-font-size)] tracking-[var(--bodytext-md-md-letter-spacing)] leading-[var(--bodytext-md-md-line-height)]">
          User Intent Classification
        </div>

        <div className="flex flex-col items-center justify-center gap-6 px-3 py-0 w-full rounded-2xl">
          {/* Interactive Intent Classification Doughnut Chart */}
          <div className="relative w-[172px] h-[172px]">
            <Doughnut data={intentChartData} options={intentChartOptions} />
            <div className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none">
              <div className="font-bodytext-xs-md text-[#4e5255] text-center">
                Total
              </div>
              <div className="font-heading-h4 text-[#141416] text-center">
                9,821
              </div>
            </div>
          </div>

          {/* Chart legend */}
          <div className="flex items-center justify-center gap-6 w-full">
            {chartData.map((item, index) => (
              <div key={index} className="flex items-center gap-1">
                <div className={`w-3 h-3 rounded ${item.color}`} />
                <div className="font-bodytext-xs-md text-[#6b7183] text-[length:var(--bodytext-xs-md-font-size)] tracking-[var(--bodytext-xs-md-letter-spacing)] leading-[var(--bodytext-xs-md-line-height)]">
                  {item.label}
                </div>
                <div
                  className={`font-bodytext-sm-semibold ${item.textColor} text-[length:var(--bodytext-sm-semibold-font-size)] tracking-[var(--bodytext-sm-semibold-letter-spacing)] leading-[var(--bodytext-sm-semibold-line-height)]`}
                >
                  {item.percentage}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Intent tabs */}
        <Tabs defaultValue="provider" className="w-full">
          <TabsList className="flex h-10 gap-2 w-full border-b border-[#c5c8cb] bg-transparent p-0">
            <TabsTrigger
              value="provider"
              className="flex-1 rounded-none border-b-2 border-[#7c47e6] data-[state=active]:border-[#7c47e6] data-[state=active]:text-[#7c47e6] data-[state=inactive]:border-[#c5c8cb] data-[state=inactive]:text-[#4e5255] pb-2 pt-0 px-1 bg-transparent"
            >
              <div className="font-bodytext-sm-md px-2 py-1">Provider</div>
            </TabsTrigger>
            <TabsTrigger
              value="seeker"
              className="flex-1 rounded-none border-b-2 border-[#c5c8cb] data-[state=active]:border-[#7c47e6] data-[state=active]:text-[#7c47e6] data-[state=inactive]:border-[#c5c8cb] data-[state=inactive]:text-[#4e5255] pb-2 pt-0 px-1 bg-transparent"
            >
              <div className="font-bodytext-sm-md px-2 py-1">Seeker</div>
            </TabsTrigger>
            <TabsTrigger
              value="neutral"
              className="flex-1 rounded-none border-b-2 border-[#c5c8cb] data-[state=active]:border-[#7c47e6] data-[state=active]:text-[#7c47e6] data-[state=inactive]:border-[#c5c8cb] data-[state=inactive]:text-[#4e5255] pb-2 pt-0 px-1 bg-transparent"
            >
              <div className="font-bodytext-sm-md px-2 py-1">Neutral</div>
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value="provider"
            className="mt-4 space-y-4 overflow-hidden"
          >
            {providerPosts.map((post) => renderPostCard(post))}
          </TabsContent>

          <TabsContent value="seeker" className="mt-4">
            {/* Content for seeker tab */}
          </TabsContent>

          <TabsContent value="neutral" className="mt-4">
            {/* Content for neutral tab */}
          </TabsContent>
        </Tabs>

        <Pagination className="mt-4">
          <PaginationContent className="flex items-center gap-[7px]">
            <PaginationItem>
              <Button variant="ghost" size="icon" className="p-2 rounded-xl">
                <img
                  className="w-5 h-5"
                  alt="Previous"
                  src="/figmaAssets/arrow-left-s-line.svg"
                />
              </Button>
            </PaginationItem>

            {paginationNumbers.map((num, index) => (
              <PaginationItem key={index}>
                {num === 1 ? (
                  <PaginationLink
                    className="w-5 h-5 p-1 rounded-lg border border-solid border-[#c5c8cb] font-bodytext-sm-md text-[#141416]"
                    isActive
                  >
                    {num}
                  </PaginationLink>
                ) : (
                  <PaginationLink className="w-5 h-5 p-1 rounded-lg font-bodytext-sm-md text-[#4e5255]">
                    {num}
                  </PaginationLink>
                )}
              </PaginationItem>
            ))}

            <PaginationItem>
              <PaginationEllipsis className="w-5 h-5 p-1 rounded-lg font-bodytext-sm-md text-[#4e5255]" />
            </PaginationItem>

            <PaginationItem>
              <Button variant="ghost" size="icon" className="p-2 rounded-xl">
                <img
                  className="w-5 h-5"
                  alt="Next"
                  src="/figmaAssets/arrow-right-s-line.svg"
                />
              </Button>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </Card>
    </section>
  );
};
