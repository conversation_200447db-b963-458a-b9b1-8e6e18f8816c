import { DownloadIcon } from "lucide-react";
import React from "react";
import { Button } from "@/components/ui/button";

export const HeaderSection = (): JSX.Element => {
  return (
    <header className="flex items-center justify-between py-4 w-full">
      <h1 className="font-heading-h1 text-[#141416] text-[length:var(--heading-h1-font-size)] tracking-[var(--heading-h1-letter-spacing)] leading-[var(--heading-h1-line-height)] [font-style:var(--heading-h1-font-style)] font-[number:var(--heading-h1-font-weight)]">
        Airlines New 2025
      </h1>

      <div className="flex items-center gap-3">
        <Button
          className="flex items-center gap-2 bg-[#7c47e6] hover:bg-[#6a3bc4] text-[#f4f0ff] rounded-2xl"
          size="sm"
        >
          <DownloadIcon className="w-5 h-5" />
          <span className="font-bodytext-sm-md text-[length:var(--bodytext-sm-md-font-size)] tracking-[var(--bodytext-sm-md-letter-spacing)] leading-[var(--bodytext-sm-md-line-height)] [font-style:var(--bodytext-sm-md-font-style)] font-[number:var(--bodytext-sm-md-font-weight)]">
            Export
          </span>
        </Button>
      </div>
    </header>
  );
};
