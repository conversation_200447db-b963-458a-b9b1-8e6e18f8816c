import { ArrowUpIcon } from "lucide-react";
import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { WordCloudComponent } from "@/components/ui/word-cloud";

export const TagsSection = (): JSX.Element => {
  const [viewMode, setViewMode] = useState<'badges' | 'wordcloud'>('badges');
  
  // Create an array of tag data to map over
  const tags = [
    "#VéMáyBayGiáRẻ",
    "#ĐặtVéMáyBayNhanhChóng",
    "#ChuyếnBayTrongNước",
    "#VietnamAirlines",
    "#VietjetAirs",
    "Lịch bay Vietjet",
    "#ThờiĐiểmĐặtVéTốtNhất",
    "#ChươngTrìnhKháchHàngThânThiết",
    "#HànhLýQuốcTế",
    "#HãngHàngKhôngUyTín",
    "#ĐặtVéMáyBayGiáTốt",
    "#ChuyếnBayTiếtKiệm",
    "#KhámPháBằngMáyBay",
    "#MẹoChọnVéMáyBay",
    "#VéMáyBayKhứHồi",
    "#DuLịchQuốcTế",
    "#DuLịchTiếtKiệm",
    "#MẹoDuLịchMáyBay",
    "#ĐặtVéMáyBayOnline",
    "#KhuyếnMãiVéMáyBay",
    "#HànhTrìnhBayAnToàn",
    "#ChọnHãngHàngKhôngPhùHợp",
    "#ĐặtChỗTrựcTuyến",
    "#DuLịchBằngMáyBay",
  ];

  // Word cloud data based on tags with different frequencies
  const wordCloudData = [
    { text: "VéMáyBayGiáRẻ", value: 45 },
    { text: "ĐặtVéMáyBayNhanhChóng", value: 38 },
    { text: "ChuyếnBayTrongNước", value: 35 },
    { text: "VietnamAirlines", value: 42 },
    { text: "VietjetAirs", value: 40 },
    { text: "LịchBayVietjet", value: 28 },
    { text: "ThờiĐiểmĐặtVéTốtNhất", value: 25 },
    { text: "ChươngTrìnhKháchHàngThânThiết", value: 20 },
    { text: "HànhLýQuốcTế", value: 32 },
    { text: "HãngHàngKhôngUyTín", value: 30 },
    { text: "ĐặtVéMáyBayGiáTốt", value: 36 },
    { text: "ChuyếnBayTiếtKiệm", value: 33 },
    { text: "KhámPháBằngMáyBay", value: 22 },
    { text: "MẹoChọnVéMáyBay", value: 24 },
    { text: "VéMáyBayKhứHồi", value: 29 },
    { text: "DuLịchQuốcTế", value: 37 },
    { text: "DuLịchTiếtKiệm", value: 31 },
    { text: "MẹoDuLịchMáyBay", value: 26 },
    { text: "ĐặtVéMáyBayOnline", value: 34 },
    { text: "KhuyếnMãiVéMáyBay", value: 39 },
  ];

  return (
    <div className="flex flex-col gap-4 w-full">
      {/* Toggle buttons */}
      <div className="flex gap-2">
        <Button
          variant={viewMode === 'badges' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setViewMode('badges')}
          data-testid="button-badges-view"
        >
          Tags View
        </Button>
        <Button
          variant={viewMode === 'wordcloud' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setViewMode('wordcloud')}
          data-testid="button-wordcloud-view"
        >
          Word Cloud
        </Button>
      </div>

      {viewMode === 'badges' ? (
        <div className="flex flex-wrap items-start gap-3 w-full">
          {tags.map((tag, index) => (
            <Badge
              key={`tag-${index}`}
              variant="secondary"
              className="bg-[#e6e7e9] hover:bg-[#e6e7e9] text-[#141416] rounded-lg px-3 py-2 font-bodytext-md-md text-[16px] font-medium tracking-[0.2px] leading-6 whitespace-nowrap"
            >
              {tag}
            </Badge>
          ))}

          <Button
            variant="ghost"
            size="sm"
            className="h-10 rounded-2xl flex items-center gap-2 px-3 py-2"
          >
            <ArrowUpIcon className="w-5 h-5" />
            <span className="font-bodytext-sm-md text-[14px] font-medium tracking-[0.2px] leading-5 text-[#141416]">
              Show less
            </span>
          </Button>
        </div>
      ) : (
        /* Word Cloud View */
        <Card className="border border-[#c5c8cb] rounded-[26px] bg-[#fdfdfd]">
          <CardContent className="p-6">
            <div className="flex flex-col gap-4">
              <h3 className="font-bodytext-md-md text-[#4e5255]">
                Popular Keywords Cloud
              </h3>
              <div className="flex justify-center">
                <WordCloudComponent
                  data={wordCloudData}
                  width={800}
                  height={300}
                  className="border border-gray-100 rounded-lg"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
