import { ArrowUpRightIcon, LineChartIcon, TargetIcon } from "lucide-react";
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export const StatisticsSection = (): JSX.Element => {
  // Data for the y-axis labels
  const yAxisLabels = ["0", "500", "1000", "1500", "2000", "2500", "3000"];

  // Data for the x-axis labels (dates)
  const xAxisLabels = [
    "Jul 17",
    "Jul 18",
    "Jul 19",
    "Jul 20",
    "Jul 21",
    "Jul 22",
    "Jul 23",
    "Jul 24",
    "Jul 25",
    "Jul 26",
    "Jul 27",
    "Jul 28",
    "Jul 29",
    "Jul 30",
    "Jul 31",
  ];

  // Data for the chart bars (actual values)
  const barData = [
    2200, 1500, 2100, 2250, 2200, 2250, 2200, 1500, 2250, 1500, 
    2100, 2200, 2100, 2250, 2650, 2250, 2250, 3200, 2250, 2250, 
    3600, 2250, 2200, 2200, 2650, 2650, 3200, 3200, 3600, 3600
  ];

  // Chart.js configuration for mentions over time
  const mentionsChartData = {
    labels: xAxisLabels,
    datasets: [
      {
        label: 'Total Mentions',
        data: barData,
        backgroundColor: '#7c47e6',
        borderColor: '#7c47e6',
        borderWidth: 0,
        borderRadius: 4,
        borderSkipped: false,
      },
    ],
  };

  const mentionsChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `Mentions: ${context.raw.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false,
        },
        ticks: {
          color: '#4e5255',
          font: {
            size: 12,
          },
          maxRotation: 45,
        },
      },
      y: {
        display: true,
        grid: {
          color: '#f0f0f0',
        },
        ticks: {
          color: '#909498',
          font: {
            size: 12,
          },
          callback: function(value: any) {
            if (value >= 1000) {
              return (value / 1000) + 'K';
            }
            return value;
          },
        },
        min: 0,
        max: 4000,
      },
    },
  };

  return (
    <section className="flex items-start gap-6 relative w-full">
      <Card className="flex-1 p-6 border border-[#d9dbde] rounded-[26px] bg-[#fdfdfd]">
        <CardContent className="p-0 flex flex-col gap-4">
          <div className="flex flex-col gap-4 w-full">
            <div className="flex justify-between items-start w-full">
              <div className="flex flex-col gap-1">
                <h3 className="font-bodytext-sm-md text-[#4e5255] mt-[-1px]">
                  Total Mentions
                </h3>
                <p className="font-bodytext-sm-md text-[#909498]">
                  Total mentions in last 30 days
                </p>
              </div>
              <LineChartIcon className="w-6 h-6" />
            </div>

            <h2 className="font-heading-h2 text-[#141416]">12,450</h2>

            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <ArrowUpRightIcon className="w-6 h-6 text-[#2bb684]" />
                <span className="font-bodytext-sm-md text-[#2bb684]">23%</span>
              </div>
              <span className="font-bodytext-sm-md text-[#4e5255]">
                compared to previous period
              </span>
            </div>
          </div>

          {/* Interactive Bar Chart */}
          <div className="h-[336px] w-full">
            <Bar data={mentionsChartData} options={mentionsChartOptions} />
          </div>
        </CardContent>
      </Card>

      <div className="flex flex-col w-[335px] gap-6 self-stretch">
        <Card className="flex-1 p-4 py-6 border border-[#d9dbde] rounded-[26px] bg-[#fdfdfd]">
          <CardContent className="p-0 flex flex-col gap-6">
            <div className="flex justify-between items-start w-full">
              <div className="flex flex-col gap-1">
                <h3 className="font-bodytext-md-md text-[#4e5255] mt-[-1px]">
                  Total Source
                </h3>
                <p className="font-bodytext-sm-md text-[#909498]">
                  Page/Group/Profile
                </p>
              </div>
              <TargetIcon className="w-6 h-6" />
            </div>

            <h2 className="font-heading-h2 text-[#141416] flex-1">123</h2>

            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <ArrowUpRightIcon className="w-6 h-6 text-[#2bb684]" />
                <span className="font-bodytext-sm-md text-[#2bb684]">2.5%</span>
              </div>
              <span className="font-bodytext-sm-md text-[#4e5255]">
                compared to previous period
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="flex-1 p-4 py-6 border border-[#d9dbde] rounded-[26px] bg-[#fdfdfd]">
          <CardContent className="p-0 flex flex-col gap-4">
            <div className="flex justify-between items-start w-full">
              <div className="flex flex-col gap-1 flex-1">
                <h3 className="font-bodytext-md-md text-[#4e5255] mt-[-1px]">
                  Total Reactions
                </h3>
                <p className="font-bodytext-sm-md text-[#909498]">
                  The total number of likes, loves, wows, and other emotional
                  responses
                </p>
              </div>
              <LineChartIcon className="w-6 h-6" />
            </div>

            <h2 className="font-heading-h2 text-[#141416] flex-1">12,450</h2>

            <div className="flex items-center gap-2 w-full">
              <div className="flex items-center gap-1">
                <ArrowUpRightIcon className="w-6 h-6 text-[#2bb684]" />
                <span className="font-bodytext-sm-md text-[#2bb684]">23%</span>
              </div>
              <span className="font-bodytext-sm-md text-[#4e5255]">
                compared to previous period
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};
