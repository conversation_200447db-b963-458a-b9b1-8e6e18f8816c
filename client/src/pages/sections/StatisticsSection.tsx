import React from "react";
import { StatisticsGrid } from "@/components/organisms/StatisticsGrid";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export const StatisticsSection = (): JSX.Element => {
  // Data for the y-axis labels
  const yAxisLabels = ["0", "500", "1000", "1500", "2000", "2500", "3000"];

  // Data for the x-axis labels (dates)
  const xAxisLabels = [
    "Jul 17",
    "Jul 18",
    "Jul 19",
    "Jul 20",
    "Jul 21",
    "Jul 22",
    "Jul 23",
    "Jul 24",
    "Jul 25",
    "Jul 26",
    "Jul 27",
    "Jul 28",
    "Jul 29",
    "Jul 30",
    "Jul 31",
  ];

  // Data for the chart bars (actual values)
  const barData = [
    2200, 1500, 2100, 2250, 2200, 2250, 2200, 1500, 2250, 1500, 
    2100, 2200, 2100, 2250, 2650, 2250, 2250, 3200, 2250, 2250, 
    3600, 2250, 2200, 2200, 2650, 2650, 3200, 3200, 3600, 3600
  ];

  // Chart.js configuration for mentions over time
  const mentionsChartData = {
    labels: xAxisLabels,
    datasets: [
      {
        label: 'Total Mentions',
        data: barData,
        backgroundColor: '#7c47e6',
        borderColor: '#7c47e6',
        borderWidth: 0,
        borderRadius: 4,
        borderSkipped: false,
      },
    ],
  };

  const mentionsChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `Mentions: ${context.raw.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false,
        },
        ticks: {
          color: '#4e5255',
          font: {
            size: 12,
          },
          maxRotation: 45,
        },
      },
      y: {
        display: true,
        grid: {
          color: '#f0f0f0',
        },
        ticks: {
          color: '#909498',
          font: {
            size: 12,
          },
          callback: function(value: any) {
            if (value >= 1000) {
              return (value / 1000) + 'K';
            }
            return value;
          },
        },
        min: 0,
        max: 4000,
      },
    },
  };

  return (
    <StatisticsGrid
      mentionsData={{
        value: '12,450',
        trend: {
          value: '23%',
          direction: 'up',
          description: 'compared to previous period'
        },
        chartData: mentionsChartData,
        chartOptions: mentionsChartOptions
      }}
      sourceData={{
        value: '123',
        subtitle: 'Page/Group/Profile'
      }}
      engagementData={{
        value: '12,450',
        trend: {
          value: '23%',
          direction: 'up',
          description: 'compared to previous period'
        }
      }}
    />
  );
};
